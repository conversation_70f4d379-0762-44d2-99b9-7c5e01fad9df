APP_NAME=openapi
APP_ENV=production
APP_KEY=base64:9VNtVzJXlaSA3qlbXj7zh0o32pf6GHgRkGOVK4zFwdg=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=rm-bp1099xns55hg0c98.mysql.rds.aliyuncs.com
DB_PORT=3306
DB_DATABASE=scinew
DB_USERNAME=sciclubs
DB_PASSWORD=sciclubs7654321

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=redis
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=r-bp10u60t3uw1ywtom4.redis.rds.aliyuncs.com
REDIS_PASSWORD=shiyanjia:shiyanjia@2021
REDIS_PORT=6379
REDIS_CLIENT=phpredis

MAIL_DRIVER=smtp
MAIL_HOST=smtpdm.aliyun.com
MAIL_PORT=465
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD="mhx9u34AKuUDehME"
MAIL_ENCRYPTION=ssl

IP_LIMIT=127.0.0.1,*************,*************,*************,**************,************

LOG_DIR=/data/logs/

CONSTANTS_SHIYANJIA_URL="https://www.shiyanjia.com"
CONSTANTS_NOTIFY_URL="https://notify.shiyanjia.com/"
CONSTANTS_CRM_URL="https://crm.shiyanjia.com"
CONSTANTS_CRMAPI_URL="https://crmapi.shiyanjia.com"
CONSTANTS_ACTIVITY_URL="https://activity.shiyanjia.com"
CONSTANTS_REPORT_SERVICE_URL="http://*************/standard-report"
CONSTANTS_CRM_V2_URL="https://crmv2.shiyanjia.com"
CONSTANTS_NEXT_CRM_URL="https://next-crm.shiyanjia.com"
CONSTANTS_SUPPLIER_URL="https://s.shiyanjia.com"
CONSTANTS_ADMIN_URL="https://admin.shiyanjia.com"
#小程序的环境参数 正式版为 release，体验版为 trial，开发版为 develop
CONSTANTS_ENV_VERSION="release"
#团体券包id
CONSTANTS_GROUP_COUPONID=1248
CONSTANTS_AI_SERVICE_URL="http://*************/ai-service"
#阿里云市场调用参数
CONSTANTS_APPCODE=4aa9c801cf744c4d8f95b6b8974ef9de
CONSTANTS_APPSECRET=4aa9c801cf744c4d8f95b6b8974ef9de
CONSTANTS_APPKEY=23605472
CONSTANTS_DATA_SERVICE_API="http://*************/data-service/"

SMS_API="http://*************:82/sendSms/index.php?op=sendSms&cip=%s&content=%s&appid=1&mobile=%s"
SMS_SEND="http://127.0.0.1:81/send_sms"

INVOICE_NNFP_APP_KEY="73846855"
INVOICE_NNFP_APP_SECRET="7B942BF733614D3C"
INVOICE_NNFP_URL="https://sdk.nuonuo.com/open/v1/services"
DIGITAL_INVOICE_NNFP_APP_KEY="31357561"
DIGITAL_INVOICE_NNFP_APP_SECRET="69072ED000284F42"
FAST_INVOICE_APP_KEY="92650482"
FAST_INVOICE_APP_SECRET="D5E0211357A84662"
NEXT_SHIYANJIA_BASE_URL="https://next.shiyanjia.com"
K8S_INGRESS_IP=*************
CRM_SERVICE_HOST=crm.shiyanjia.com


#探索型订单buffetid
EXPLORATORY_BUFFET_ID_LIST = 959,971
EXPLORATORY_EXPERIMENTAL_UNIT_BUFFET_ID= 32,40

OSS_ACCESSKEY_ID=LTAI5tFrEhhu7ZxLhGaxzRmA
OSS_ACCESSKEY_SECRET=******************************
OSS_END_POINT=oss-cn-hangzhou.aliyuncs.com
OSS_INTRANET_END_POINT=oss-cn-hangzhou-internal.aliyuncs.com
OSS_REPORT_BUCKET=shiyanjia-reports

OSS_PAD_ACCESSKEY_ID=LTAI5tD969dazEmAJHVeQmeL
OSS_PAD_ACCESSKEY_SECRET=******************************

ORDER_SUPPLEMENTARY_AUTH_GIVE_COUPONID=1915

AUTH_EXAMINE_NOTICE_ROLE_ID=523
AUTH_EXAMINE_NOTICE_LEGAL_ROLE_ID=559

NUONUO_INVOICE_WECOM_ROBOT_WEBHOOK_URL='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=08cbd271-77df-452f-8c3c-308cd4e7fd53'

SF_ACCESS_TOKEN_API=https://sfapi.sf-express.com/oauth2/accessToken
SF_PARTNER_ID=YQXXJX1LC0FQ
SF_SECRET=lXn4UphbfTux64f5jJz5QK77W1BhuIyv

TIAN_YAN_CHA_SEARCH_TOKEN='1fdcea86-4868-4543-ba80-8674ca42ba4a'
TIAN_YAN_CHA_SEARCH_CORPORATION_TOKEN='9f9e6766-9950-4572-8b80-7e8b06181895'
LE_CHENG_APPID="lc3f60cc6926a24447"
LE_CHENG_APP_SECRET="******************************"
LE_CHENG_URL="https://openapi.lechange.cn:443/openapi"
LE_HLS_KEYWORD = "https://"

SEND_WECHAT_MESSAGE_IP="**************"

CRM_ACCOUNT_ID_SYSTEM=1142
ADMIN_ACCOUNT_ID_SYSTEM=450

#纳米商城默认不可用优惠券id
NANO_UN_AVAILABLE_COUPON_IDS=2459,2458,2352,2353,841,11,1234

CREATE_NOT_AUTH_GROUP_PERMISSION=656

#百度接口配置
BAIDU_INVOICE_API_KEY=XGhcNEU2hrugPXddKMcuO2Pd
BAIDU_INVOICE_SECRET_KEY=7GjHbo2qn5Squxz8PYYQpWP6uNFQZ5Gh

#易快报接口配置
EKUAIBAO_URL_PREFIX=https://wx2.ekuaibao.com
EKUAIBAO_APP_KEY=ecf7862a-c938-49c8-97c5-e8114fd9a492
EKUAIBAO_APP_SECURITY=7c6ab32a-8a99-4f64-ae67-f9d9cca2c50a

#业务相关
REPORT_UPLOADED_NOTIFY_BUFFET_IDS=228

#Yanqulib
YANQU_LIB_PATH=/home/<USER>/yanqu-phplib/

##mns主题名称
MNS_COMMON_EVENT_TOPIC_NAME=prod-common-event