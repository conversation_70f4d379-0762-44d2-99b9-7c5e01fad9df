<?php

namespace App\Services\Account;

use App\Repositories\Account\AccountInfoRepository;
use App\Repositories\Account\AccountEmailRepository;

class AccountEmailService
{
    /**
     *
     * @param $params
     * @return array
     */
    public function getEmails($params): array
    {
        $accountId = (int)($params['account_id'] ?? 0);
        $allEmails = [];
        $accountInfo = app(AccountInfoRepository::class)->getAccountInfo($accountId);
        if (!empty($accountInfo) && !empty($accountInfo->email)) {
            $allEmails[] = $accountInfo->email;
        }
        $emails = app(AccountEmailRepository::class)->getAccountEmailsByAccountId($accountId);
        $emails = $emails->pluck('email')->toArray();
        $allEmails = array_merge($allEmails, $emails);
        $allEmails = array_values(array_unique(array_filter($allEmails)));
        return ['list' => $allEmails];
    }

    /**
     * @param $params
     * @return void
     */
    public function addEmail($params)
    {
        $accountId = (int)($params['account_id'] ?? 0);
        $email = trim($params['email'] ?? '');
        $accountEmailRepository = app(AccountEmailRepository::class);
        $exists = $accountEmailRepository->emailExists($accountId, $email);
        if ($exists) {
            return;
        }
        $data = [
            'accountid' => $accountId,
            'email' => $email,
            'isvoid' => 0,
            'addtime' => date('Y-m-d H:i:s'),
        ];
        $accountEmailRepository->insert($data);
    }
}