<?php
/**
 * 用户
 */

namespace App\Services;


use App\Constants\AchievementConstants;
use App\Constants\BusinessAreaConstants;
use App\Constants\CompanyConstants;
use App\Constants\Constant;
use App\Http\Controllers\Constants\Card\CardConstants;
use App\Models\AccountCard;
use App\Models\AccountCredit;
use App\Models\AccountCreditHistory;
use App\Models\AccountInfo;
use App\Models\BuffetAddress;
use App\Models\MapResearchgroupMember;
use App\Models\OrderInfo;
use App\Models\Regions;
use App\Models\Univs;
use App\Models\UserAgreement;
use App\Models\UserAgreementAgree;
use App\Utils\Region\Ip2Region;
use Illuminate\Support\Facades\DB;
use App\Utils\LogHelper;
use Illuminate\Support\Facades\Redis;
use App\Models\DealerAccount;
use Yanqu\YanquPhplib\Openapi\Dealer\Constants\DealerStatus;
class UserService extends BaseService
{
    /**
     * 用户同意协议记录
     * @param $accountId
     * @param $userIp
     * @param $agreementCode
     * @param $scene
     */
    public function userAgreeAgreementLog($accountId, $distinctId, $userIp, $agreementCode, $scene)
    {
        //查询协议id
        $agreementCode = json_decode($agreementCode, true);
        foreach ($agreementCode as $code) {
            $agreementId = UserAgreement::where("code", $code)
                ->where("has_used", UserAgreement::IN_USED)->value('id');
            if ($agreementId > 0) {
                UserAgreementAgree::insert([
                    "agreement_id" => $agreementId,
                    "accountid"    => $accountId,
                    "distinct_id"  => $distinctId,
                    "ip"           => $userIp,
                    "scene"        => $scene,
                ]);
            }
        }
        return true;
    }

    public function updateMemberGrade($accountId)
    {
        try {
            //获取用户不欠款且完成订单总金额
            $totalAmount = $this->totalOrderAmount($accountId);

            //计算订单金额对应的等级
            $grade = $this->totalAmountMappingGrade($totalAmount);

            //更新额度
            $this->updateUserCreditAmount($accountId, $grade);

            return true;
        } catch (\Exception $e) {
            LogHelper::doLog(
                "openapi_update_member_grade",
                json_encode(["data" => ["accountid" => $accountId], "message" => "会员等级更新失败"], JSON_UNESCAPED_UNICODE)
            );
            return false;
        }
    }

    public function totalOrderAmount($accountId)
    {
        $totalAmount = 0.00;
        $userOrders = OrderInfo::where("accountid", $accountId)
            ->where("orderstate", OrderInfo::ORDER_STATE_COMPLETE)
            ->select("oid", "osn", "offlinepayamount", "onlinepayamount", "couponpayamount", "grouppayamount", "creditpayamount")
            ->get();
        if ($userOrders) {
            for ($k = 0; $k < count($userOrders); $k++) {
                if ($userOrders[$k]->creditpayamount != null &&
                    $userOrders[$k]->creditpayamount != "" &&
                    floatval($userOrders[$k]->creditpayamount) > 0.0) {
                    $tmpCreateHistoryInfo = AccountCreditHistory::where("accountid", $accountId)
                        ->where("orderid", $userOrders[$k]->oid)
                        ->where("isvoid", Constant::NOT_VOID)
                        ->where("isetted", AccountCreditHistory::CREDIT_HAS_PAYED)
                        ->first();
                    if ($tmpCreateHistoryInfo != null && count($tmpCreateHistoryInfo) > 0) {
                        $totalAmount = $totalAmount + (floatval($userOrders[$k]->offlinepayamount) + floatval($userOrders[$k]->onlinepayamount) + floatval($userOrders[$k]->couponpayamount) + floatval($userOrders[$k]->grouppayamount) + floatval($userOrders[$k]->creditpayamount));
                    }
                } else {
                    $totalAmount = $totalAmount + (floatval($userOrders[$k]->offlinepayamount) + floatval($userOrders[$k]->onlinepayamount) + floatval($userOrders[$k]->couponpayamount) + floatval($userOrders[$k]->grouppayamount) + floatval($userOrders[$k]->creditpayamount));
                }
            }
        }
        return $totalAmount;
    }

    public function totalAmountMappingGrade($amount)
    {
        if ($amount < CardConstants::GOLD_MEMBER_ORDER_AMOUNT) {
            $grade = CardConstants::MEMBER_GRADE_BRONZE;
        } elseif ($amount >= CardConstants::GOLD_MEMBER_ORDER_AMOUNT && $amount < CardConstants::PLATINUM_MEMBER_ORDER_AMOUNT) {
            $grade = CardConstants::MEMBER_GRADE_GOLD;
        } elseif ($amount >= CardConstants::PLATINUM_MEMBER_ORDER_AMOUNT) {
            $grade = CardConstants::MEMBER_GRADE_PLATINUM;
        }
        return $grade;
    }

    public function updateUserCreditAmount($accountId, $userNewGrade)
    {
        //获取用户会员信息
        $cardInfo = AccountCard::where("accountid", $accountId)
            ->where("is_group_open_credit", CardConstants::IS_GROUP_OPEN_CREDIT_FALSE)
            ->where("status", AccountCard::EXAMINE_SUCCESS)
            ->first();
        if (!$cardInfo) {
            //未开通要会员，不用操作
            return true;
        }
        //获取用户身份类型
        if ($cardInfo->cardrank < $userNewGrade) {
            $creditInfo = AccountCredit::where("accountid", $accountId)->where("isvoid", Constant::VOID)->first();
            //用户只有加入实验家会员才可以自动提额
            if ($cardInfo->status == AccountCard::EXAMINE_SUCCESS) {
                $cardRank = $cardInfo->cardrank;
                //如果以前等级小于最新等级，则升级
                if ($cardRank < $userNewGrade) {
                    AccountCard::where('accountid', $accountId)->update(["cardrank" => $userNewGrade]);
                }
                //计算出提升的差额
                $diffQuota = 0;
                $oldQuota = isset($creditInfo->quota) ? $creditInfo->quota : 0;
                if ($cardRank == CardConstants::MEMBER_GRADE_BRONZE) {
                    if ($userNewGrade == CardConstants::MEMBER_GRADE_GOLD) {
                        $diffQuota = CardConstants::GOLD_MEMBER_AMOUNT - $oldQuota;
                    } elseif ($userNewGrade == CardConstants::MEMBER_GRADE_PLATINUM) {
                        $diffQuota = CardConstants::PLATINUM_MEMBER_AMOUNT - $oldQuota;
                    }
                } elseif ($cardRank == CardConstants::MEMBER_GRADE_GOLD) {
                    $diffQuota = CardConstants::PLATINUM_MEMBER_AMOUNT - $oldQuota;
                }
                //提升的差额大于0时 才去更新quota(额度)和bequota(剩余额度)
                if ($diffQuota > 0) {
                    if ($creditInfo) {
                        AccountCredit::query()->where('accountid', $accountId)
                            ->update([
                                'quota'             => DB::raw("quota+$diffQuota"),
                                'bequota'           => DB::raw("bequota+$diffQuota"),
                                'quotaupdatemethod' => AccountCredit::QUTOA_UPDATE_METHOD_PC
                            ]);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取积分系数
     */
    public function getUserIntCoeff($userNewGrade)
    {
        switch ($userNewGrade) {
            case CardConstants::MEMBER_GRADE_BRONZE;
                $intCoeff = CardConstants::BRONZE_MEMBER_INTCOEFF;
                break;
            case CardConstants::MEMBER_GRADE_GOLD;
                $intCoeff = CardConstants::GOLD_MEMBER_INTCOEFF;
                break;
            case CardConstants::MEMBER_GRADE_PLATINUM;
                $intCoeff = CardConstants::PLATINUM_MEMBER_INTCOEFF;
                break;
            default:
                $intCoeff = CardConstants::BRONZE_MEMBER_INTCOEFF;
        }
        return $intCoeff;
    }

    public function getUserIntegralCoefficient($accountId)
    {
        $cardInfo = AccountCard::where("accountid", $accountId)
            ->where("is_group_open_credit", CardConstants::IS_GROUP_OPEN_CREDIT_FALSE)
            ->first();
        if ($cardInfo && $cardInfo->cardrank > 0) {
            return $this->getUserIntCoeff($cardInfo->cardrank);
        } else {
            return CardConstants::BRONZE_MEMBER_INTCOEFF;
        }
    }

    public function updateMemberGradeInsertQueue($content)
    {
        $ret = Redis::lPush(CardConstants::UPDATE_MENBER_GRADE, $content);
        if ($ret == 0) {
            LogHelper::doLog("openapi_update_member_grade", $content);
        }
        return $ret;
    }


    //是否是新能源客户
    public function isNewEnergyAccount($accountId): bool
    {
        if (empty($accountId)) {
            return false;
        }
        $info = AccountInfo::where("accountid", $accountId)->select([
            "industry", "accountype", "company_id"
        ])->first();
        if (empty($info) || $info->accountype !== AccountInfo::ACCOUNT_TYPE_COMPANY) {
            return false;
        }
        $standardCompanyId = DB::table("company_mount")
            ->where("mount_company_id", $info->company_id)->value("company_id");
        $tagRelatedId = DB::table("company_tag_related")->where("company_id", $standardCompanyId)
            ->where("isvoid",Constant::NOT_VOID)
            ->where("company_tag_id", CompanyConstants::COMPANY_TAG_ID_NEW_ENERGY)->value("id");
        $hasTagRelated = DB::table("company_tag_related")->where("company_id", $standardCompanyId)
            ->where("isvoid",Constant::NOT_VOID)
            ->value("id");
        //如果企业打过标签了，就以企业的标签为准
        if ($hasTagRelated) {
            return $tagRelatedId > 0;
        }
        //如果企业没打过标签，就以注册时的标签为准
        return $info->industry === CompanyConstants::ACCOUNT_INDUSTRY_NEW_ENERGY;
    }

    /**
     * 获取用户信息
     * @param $accountId
     * @param string[] $field
     * @return AccountInfo|array|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|\Illuminate\Database\Query\Builder|object
     */
    public function getAccountInfo($accountId,$field = ['*'])
    {
        $accountInfo = [];
        if(!empty($accountId)) {
            $accountInfo = AccountInfo::query()->where("accountid", $accountId)->select($field)->first();
        }
        return !empty($accountInfo) ? $accountInfo : [];
    }

    /**
     * 获取用户所属分部地址
     * @param $accountInfo
     * @param $userIp
     * @return int
     */
    public function getBranchAddressId($accountInfo,$userIp):int
    {
        $cityId = !empty($accountInfo->city) ? $accountInfo->city : $this->getCityIdByIp($userIp);
        //获取分部
        if(empty($cityId)) {
            $addressId = BusinessAreaConstants::OVERSEAS_DEFAULT_BRANCH; //解析不了ip城市默认海外给深圳
        }else {
            $addressId = $this->getBranchAddressIdByCityId($cityId); //海外归深圳，其他正常取值
        }
        return $addressId;
    }

    /**
     * 根据城市获取办事处id
     * @param $cityId
     * @return int
     */
    public function getBranchAddressIdByCityId($cityId):int
    {
        $cityInfo = Regions::query()->where('regionid',$cityId)
            ->where('layer',2)
            ->select('regionid','parentid')
            ->first();
        $addressId = 0;
        if(!empty($cityInfo)) {
            //存在海外的城市新增了regions，但是buffet_address里面有没有更新绑定
            $addressId = BuffetAddress::query()->whereRaw("FIND_IN_SET('".$cityId."',cityids)")->value('addressid');
        }
        if(empty($addressId)) {
            $addressId = BusinessAreaConstants::OVERSEAS_DEFAULT_BRANCH; //取不到的默认指派深圳
        }
        return $addressId;
    }

    /**
     * 根据Ip获取城市id
     * @param string $ip
     * @return \Illuminate\Database\Eloquent\HigherOrderBuilderProxy|int|mixed
     */
    public function getCityIdByIp(string $ip = '')
    {
        $cityId = 0;
        if(empty($ip)) {
            return $cityId;
        }
        //如果ip中包含逗号，则取第一个
        if(strpos($ip,",") !== false) {
            $ips = explode(",",$ip);
            $ip = trim($ips[0]);
        }
        $ipCityInfo = (new RegisterService())->getAliyunIPCityName($ip);
        $cityName = $ipCityInfo["city"];
        if (!empty($cityName)) {
            $cityInfo = Regions::query()->where("layer", 2)->where("name", "like", "%{$cityName}%")->first();
            if (!empty($cityInfo)) {
                $cityId = $cityInfo->regionid;
            }
        }
        return $cityId;
    }

    public function getUserAchievementsHead($params, $achievements)
    {
        $accountId = isset($params['accountid']) ? $params['accountid'] : 0;
        $accountType = isset($params['accountype']) ? $params['accountype'] : "";
        $province = isset($params['province']) ? $params['province'] : 0;
        $city = isset($params['city']) ? $params['city'] : 0;
        $universityId = isset($params['universityid']) ? $params['universityid'] : 0;
        $majorId = isset($params['majorid']) ? $params['majorid'] : 0;
        $companyName = isset($params['companyname']) ? $params['companyname'] : "";
        $hospitalId = isset($params['hospitalid']) ? $params['hospitalid'] : 0;
        $postIp = isset($params['postip']) ? $params['postip'] : "";
        $source = isset($params['source']) ? $params['source'] : "";
        $saccountid = 0;
        if ($accountId > 0) {
            $accountinfo = DB::table('account_info')->where('accountid', $accountId)->first();
            $accountinfo = json_decode(json_encode($accountinfo), true);
            if (empty($accountinfo)) {
                return ["status" => false, "message" => '用户不存在，请校验'];
            }
        } else {
            $accountinfo = [];
            $accountinfo['accountype'] = $accountType;
            $accountinfo['province'] = $province;
            $accountinfo['city'] = $city;
            $accountinfo['universityid'] = $universityId;
            $accountinfo['majorid'] = $majorId;
            $accountinfo['companyname'] = $companyName;
            $accountinfo['hospitalid'] = $hospitalId;
            $accountinfo['postip'] = $postIp;
            $accountinfo['company_id'] = '';
        }

        //如果是安徽的，且指定来源，返回科科
        if(
            in_array($source, AchievementConstants::WECOM_CODE_SOURCE, true)
            && $accountinfo['province'] == 12
        ){
            return ["status" => true, "data" => ["saccountid" => AchievementConstants::KEKE_CRM_ID]];
        }

        //如果是金华和绍兴的企业客户，就返回罗健
        if((int)$accountinfo["accountype"] === AccountInfo::ACCOUNT_TYPE_COMPANY &&
            in_array($accountinfo['city'], [126, 127])
        ) {
            return ["status" => true, "data" => ["saccountid" => AchievementConstants::LUOJIAN_CRM_ID]];
        }



        //高校用户对应学院为空取其他院系
        if (empty($accountinfo['majorid']) && !empty($accountinfo['universityid'])) {
            $majorId = DB::table("univs_dep")->where("univscode", $accountinfo['universityid'])
                ->whereIn('depname', ["其他院系","其它院系"])
                ->value("depcode");
        } else {
            $majorId = $accountinfo['majorid'];
        }
        //查找学院
        if (!empty($majorId) && in_array($accountinfo['accountype'], [1, 2])) {
            foreach ($achievements as $sik) {
                $jxsaccountid = $sik->saccountid;
                $tmp_depcodes = $sik->depcodes;  //学院集合id
                $tmp_depcodes = explode(",", $tmp_depcodes);
                foreach ($tmp_depcodes as $tmp_depcode) {
                    if (!empty($tmp_depcode)) {
                        $depcode = explode('_', $tmp_depcode);
                        $depcode = $depcode[1];
                        if ($depcode == $majorId) {
                            $saccountid = $jxsaccountid;
                        }
                    }
                }
            }
        }

        //查学校
        if ($saccountid == 0) {
            $universityid = $accountinfo['universityid'];
            if ($universityid != null && $universityid != "" && in_array($accountinfo['accountype'], [1, 2])) {
                foreach ($achievements as $sik) {
                    $jxsaccountid = $sik->saccountid;
                    $tmp_univscodes = $sik->univscodes;  //学校名称
                    $tmp_univscodes = explode(",", $tmp_univscodes);
                    for ($siki = 0; $siki < count($tmp_univscodes); $siki++) {
                        if (trim($tmp_univscodes[$siki]) == trim($universityid)) {
                            $saccountid = $jxsaccountid;
                        }
                    }
                }
            }
        }

        //查询企业
        if ($saccountid == 0) {
            if ($accountinfo['company_id'] != '' && $accountinfo['company_id'] != null
                && $accountinfo['accountype'] == 4) {
                foreach ($achievements as $sik) {
                    $jxsaccountid = $sik->saccountid;
                    $tmp_not_standard_company_ids = $sik->not_standard_company_ids;  //非标准企业id集合
                    $tmp_not_standard_company_ids = explode(",", $tmp_not_standard_company_ids);
                    for ($siki = 0; $siki < count($tmp_not_standard_company_ids); $siki++) {
                        if (trim($tmp_not_standard_company_ids[$siki]) == trim($accountinfo['company_id'])) {
                            $saccountid = $jxsaccountid;
                        }
                    }
                }
            }
            //兼容通过用户端获取专属客服
            if ($accountinfo['companyname'] != '' && $accountinfo['companyname'] != null &&
                $accountinfo['accountype'] == 4 && $accountId == 0) {
                foreach ($achievements as $sik) {
                    $jxsaccountid = $sik->saccountid;
                    $tmp_companys = $sik->companys;  //企业集合
                    $tmp_companys = explode(",", $tmp_companys);
                    for ($siki = 0; $siki < count($tmp_companys); $siki++) {
                        if (trim($tmp_companys[$siki]) == trim($accountinfo['companyname'])) {
                            $saccountid = $jxsaccountid;
                        }
                    }
                }
            }
        }

        //查医院
        if ($saccountid == 0) {
            $tmphospitalid = $accountinfo['hospitalid'];
            if ($tmphospitalid != null && $tmphospitalid != "" && $tmphospitalid > 0
                && $accountinfo['accountype'] == 6) {
                foreach ($achievements as $sik) {
                    $jxsaccountid = $sik->saccountid;
                    $tmp_hospitalids = $sik->hospitalids;
                    $tmp_hospitalids = explode(",", $tmp_hospitalids);
                    for ($siki = 0; $siki < count($tmp_hospitalids); $siki++) {
                        if (trim($tmp_hospitalids[$siki]) == trim($tmphospitalid)) {
                            $saccountid = $jxsaccountid;
                        }
                    }
                }
            }
        }

        //如果到这里还没找到负责人，且账号的城市（目前是深圳）是韩榭直接兜底负责的
        if ($saccountid == 0
            &&
            in_array((int)$accountinfo['city'], AchievementConstants::HANXIE_ACHIEVEMENT_CITIES, true)) {
            $saccountid = AchievementConstants::HANXIE_CRM_ID;
        }


        //查城市
        if ($saccountid == 0) {
            $tmpcity = $accountinfo['city'];
            if ($tmpcity != null && $tmpcity != "" && $tmpcity > 0) {
                foreach ($achievements as $sik) {
                    $jxsaccountid = $sik->saccountid;
                    $tmp_citys = $sik->citys;
                    $tmp_citys = explode(",", $tmp_citys);
                    for ($siki = 0; $siki < count($tmp_citys); $siki++) {
                        if (trim($tmp_citys[$siki]) == trim($tmpcity)) {
                            $saccountid = $jxsaccountid;
                        }
                    }
                }
            }
        }

        //查省份
        if ($saccountid == 0) {
            $tmpprovince = $accountinfo['province'];
            if ($tmpprovince != null && $tmpprovince != "" && $tmpprovince > 0) {
                foreach ($achievements as $sik) {
                    $jxsaccountid = $sik->saccountid;
                    $tmp_provinces = $sik->provinces;

                    $tmp_provinces = explode(",", $tmp_provinces);
                    for ($siki = 0; $siki < count($tmp_provinces); $siki++) {
                        if (trim($tmp_provinces[$siki]) == trim($tmpprovince)) {
                            $saccountid = $jxsaccountid;
                        }
                    }
                }
            }
        }

        //根据城市查区域负责人
        if ($saccountid == 0 && $accountinfo['city'] > 0) {
            $sql = "SELECT * FROM `sci_buffet_address` WHERE CONCAT(',',cityids,',') LIKE '%," .
                $accountinfo['city'] . ",%'";
            $data = DB::select($sql);
            if ($data != "" and $data != null) {
                $data = $data[0];
                if ($data->couponsaccountid > 0) {
                    $saccountid = $data->couponsaccountid;
                    //判断crm账号是否异常
                    $crm_account = DB::table('crm_account')
                        ->where('accountid', $saccountid)
                        ->where('status', 100)
                        ->where('isnormal', 1)
                        ->where('isvoid', 0)
                        ->first();
                    if (empty($crm_account)) {
                        $saccountid = 0;
                    }
                }
            }
        }

        //根据IP查找 城市/省份 and 区域负责人
        if ($accountinfo['postip'] != '' && $accountinfo['postip'] != null && $saccountid == 0) {
            // 使用统一的IP获取城市方法
            $tmpcity = $this->getCityIdByIp($accountinfo['postip']);
            if ($tmpcity > 0) {
                // 先按绩效归属查找城市负责人
                foreach ($achievements as $sik) {
                    $jxsaccountid = $sik->saccountid;
                    $tmp_citys = $sik->citys;
                    $tmp_citys = explode(",", $tmp_citys);
                    for ($siki = 0; $siki < count($tmp_citys); $siki++) {
                        if (trim($tmp_citys[$siki]) == trim($tmpcity)) {
                            $saccountid = $jxsaccountid;
                        }
                    }
                }
                
                // 如果找不到城市负责人，查找省负责人
                if ($saccountid == 0) {
                    $provinceId = Regions::query()->where('regionid', $tmpcity)->value('parentid');
                    if ($provinceId > 0) {
                        foreach ($achievements as $sik) {
                            $jxsaccountid = $sik->saccountid;
                            $tmp_provinces = $sik->provinces;
                            $tmp_provinces = explode(",", $tmp_provinces);
                            for ($siki = 0; $siki < count($tmp_provinces); $siki++) {
                                if (trim($tmp_provinces[$siki]) == trim($provinceId)) {
                                    $saccountid = $jxsaccountid;
                                }
                            }
                        }
                    }
                }
                
                // 如果还找不到，查找buffet_address表中的区域负责人
                if ($saccountid == 0) {
                    $sql = "SELECT * FROM `sci_buffet_address` WHERE CONCAT(',',cityids,',') LIKE '%," .
                        $tmpcity . ",%'";
                    $data = DB::select($sql);
                    if ($data != "" and $data != null) {
                        $data = $data[0];
                        if ($data->leader > 0) {
                            $saccountid = $data->leader;
                            //判断crm账号是否异常
                            $crm_account = DB::table('crm_account')
                                ->where('accountid', $saccountid)
                                ->where('status', 100)
                                ->where('isnormal', 1)
                                ->where('isvoid', 0)
                                ->first();
                            if (empty($crm_account)) {
                                $saccountid = 0;
                            }
                        }
                    }
                }
            }
        }

        //没有的话默认 刘千 或者凌磊
        if ($saccountid == 0) {
            if ($source === "ai-service") {
                $saccountid = 941;//凌磊
            } else {
                $saccountid = AchievementConstants::HANXIE_CRM_ID;
            }
        }
        //查询是是否存在大客户品牌负责人
        $keyAccountBrandId = $this->getKeyAccountBrandId($accountId);
        if($keyAccountBrandId > 0){
            $saccountid = $keyAccountBrandId;
        }
        return ["status" => true, "data" => ["saccountid" => $saccountid]];
    }

    public function getKeyAccountBrandId($accountId)
    {
        $crmId = 0;
        if($accountId > 0){
            $teacherGroupId = AccountInfo::where("accountid",$accountId)
                ->whereIn("accountype",[AccountInfo::ACCOUNT_TYPE_STU,AccountInfo::ACCOUNT_TYPE_TUTOR])
                ->value('teacher_group_id');
            if($teacherGroupId > 0){
                $brandId = DB::table('key_account_teacher_group_responsible')
                    ->where("teacher_group_id", $teacherGroupId)
                    ->value('brand_manager_id');
                if (!empty($brandId)) {
                    $crmId = $brandId;
                }
            }
        }
        return $crmId;
    }
}
