<?php

namespace App\Services\Ekuaibao\EkuaibaoApi;

use App\Constants\Ekuaibao\EkuaibaoUrlConstants;
use App\Constants\Ekuaibao\FeeType\FeeTypeConstants;
use App\Utils\EnvironmentHelper;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\YqLog\YqLog;

class EkuaibaoFeeTypeService
{
    /**
     * @param $ids
     * @param $codes
     * @return array|mixed
     * @throws CurlException
     */
    public function getFeeTypeDetailByIdsAndCodes($ids = [], $codes = [])
    {
        if(empty($ids) && empty($codes)){
            return [];
        }
        $accessToken = app(EkuaibaoAuthorityService::class)->getAccessToken();
        $url = env('EKUAIBAO_URL_PREFIX') . EkuaibaoUrlConstants::GET_FEE_TYPE_BY_IDS_AND_CODES .
            '?accessToken=' . $accessToken;
        $params = [
            'ids' => $ids,
            'codes' => $codes,
        ];
        $response = CurlUtil::postJson($url, $params,[],[CURLOPT_TIMEOUT => 10]);
        YqLog::logger('finance:ekuaibao')->info('getFeeTypeDetailByIdsAndCodes', [
            'params' => $params,
            'response' => $response,
        ]);
        $data = json_decode($response, true);
        return $data['items'] ?? [];
    }

    public function getCostTestPlatformId(): string
    {
        if (EnvironmentHelper::isProduction()) {
            return FeeTypeConstants::PRODUCTION_COST_TEST_PLATFORM_ID;
        }else{
            return FeeTypeConstants::TEST_COST_TEST_PLATFORM_ID;
        }
    }

    /**
     * @throws CurlException
     */
    public function getExpenseSpecificationId()
    {
        $feeTypeDetail = $this->getFeeTypeDetailByIdsAndCodes([$this->getCostTestPlatformId()]);
        return $feeTypeDetail[0]['expenseSpecificationId'] ?? '';
    }

    /**
     * @throws CurlException
     */
    public function getRequisitionSpecificationId()
    {
        $feeTypeDetail = $this->getFeeTypeDetailByIdsAndCodes([$this->getCostTestPlatformId()]);
        return $feeTypeDetail[0]['requisitionSpecificationId'] ?? '';
    }
}