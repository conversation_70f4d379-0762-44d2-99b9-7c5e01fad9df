<?php

namespace App\Services\Ekuaibao\EkuaibaoApi;

use App\Constants\Ekuaibao\EkuaibaoUrlConstants;
use App\Entities\Ekuaibao\FlowForm\CorporatePaymentFlowFormDTO;
use App\Entities\Ekuaibao\FlowForm\PrepaymentFowFormDTO;
use App\Entities\Ekuaibao\FlowForm\PrepaymentReconciliationFlowFormDTO;
use App\Entities\Ekuaibao\MoneyDTO;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\YqLog\YqLog;

class EkuaibaoFlowService
{
    /**
     * 根据单据编号获取单据详情
     * @param $flowCode
     * @return mixed
     * @throws CurlException
     */
    public function getFlowDetailByFlowCode($flowCode)
    {
        $accessToken = app(EkuaibaoAuthorityService::class)->getAccessToken();
        $url = env('EKUAIBAO_URL_PREFIX') . EkuaibaoUrlConstants::GET_FLOW_DETAIL . '?accessToken=' . $accessToken;
        $params = [
            'code' => $flowCode,
        ];
        $response = CurlUtil::get($url, $params, [], [CURLOPT_TIMEOUT => 10]);
        YqLog::logger('finance:ekuaibao')->info('getFlowDetailByFlowCode', [
            'params' => $params,
            'response' => $response,
        ]);
        return json_decode($response, true);
    }

    /**
     * 创建单据
     * @param $params
     * @return mixed
     * @throws CurlException
     */
    public function createFlow($params)
    {
        $accessToken = app(EkuaibaoAuthorityService::class)->getAccessToken();
        $url = env('EKUAIBAO_URL_PREFIX') . EkuaibaoUrlConstants::CREATE_FLOW . '?accessToken=' . $accessToken;
        $response = CurlUtil::postJson($url, $params, [], [CURLOPT_TIMEOUT => 30]);
        YqLog::logger('finance:ekuaibao:createFlow')->info('createFlow', [
            'params' => $params,
            'response' => $response,
        ]);

        //如果返回的是json字符串，转换成数组
        $tempResponse = json_decode($response, true);
        if (json_last_error() == JSON_ERROR_NONE) {
            $response = $tempResponse;
        }
        return $response;
    }

    public function buildPayMoneyDTOBYFlowDetail($flowDetail): MoneyDTO
    {
        $amount = $flowDetail['value']['form']['payMoney']['standard'] ?? 0;
        //默认都是人民币金额
        return app(EkuaibaoMoneyService::class)->buildMoneyDTOByRmbAmount($amount);
    }

    public function buildWrittenOffMoneyDTOBYFlowDetail($flowDetail): MoneyDTO
    {
        $amount = $flowDetail['value']['form']['writtenOffMoney']['standard'] ?? 0;
        //默认都是人民币金额
        return app(EkuaibaoMoneyService::class)->buildMoneyDTOByRmbAmount($amount);
    }

    /**
     * 创建对公付款单（有发票）单据
     * @param CorporatePaymentFlowFormDTO $formDTO
     * @return mixed
     * @throws CurlException
     */
    public function createCorporatePaymentFlow(CorporatePaymentFlowFormDTO $formDTO)
    {
        $details = [];
        foreach ($formDTO->getDetails() as $detail) {
            $details[] = $detail->toCreateFlowArray();
        }
        $ekuaibaoPropertyService = app(EkuaibaoPropertyService::class);
        $params = [
            'form' => [
                'specificationId' => $formDTO->getSpecificationId(),
                'title' => $formDTO->getTitle(),
                $ekuaibaoPropertyService->getLegalEntityPropertyName() => $formDTO->getLegalEntity(),
                'submitterId' => $formDTO->getSubmitterId(),
                $ekuaibaoPropertyService->getSubmitterDepartmentPropertyName() => $formDTO->getSubmitterDepartment(),
                'expenseDepartment' => $formDTO->getExpenseDepartment(),
                $ekuaibaoPropertyService->getDepartmentAttributePropertyName() => $formDTO->getDepartmentAttribute(),
                $ekuaibaoPropertyService->getPaymentTypePropertyName() => $formDTO->getPaymentType(),
                'payeeId' => $formDTO->getPayeeId(),
                'description' => $formDTO->getDescription(),
                'details' => $details,
                $ekuaibaoPropertyService->getIsFinalPaymentPropertyName() => $formDTO->getIsFinalPayment(),
                $ekuaibaoPropertyService->getParticipantPropertyName() => [$formDTO->getParticipants()],
                $ekuaibaoPropertyService->getArchiveFlagPropertyName() => $formDTO->getArchiveFlag(),
                $ekuaibaoPropertyService->getCallbackFlagPropertyName() => $formDTO->getCallbackFlag(),
            ]
        ];
        if ($formDTO->getContractQuantity() != null) {
            $params['form'][$ekuaibaoPropertyService->getContractQuantity()] = $formDTO->getContractQuantity();
        }
        return $this->createFlow($params);
    }

    /**
     * 创建预付单（无发票）单据
     * @param PrepaymentFowFormDTO $formDTO
     * @return mixed
     * @throws CurlException
     */
    public function createPrepaymentFlow(PrepaymentFowFormDTO $formDTO)
    {
        $details = [];
        foreach ($formDTO->getDetails() as $detail) {
            $details[] = $detail->toCreateFlowArray();
        }
        $ekuaibaoPropertyService = app(EkuaibaoPropertyService::class);
        $params = [
            'form' => [
                'specificationId' => $formDTO->getSpecificationId(),
                'title' => $formDTO->getTitle(),
                $ekuaibaoPropertyService->getLegalEntityPropertyName() => $formDTO->getLegalEntity(),
                'submitterId' => $formDTO->getSubmitterId(),
                'expenseDepartment' => $formDTO->getExpenseDepartment(),
                'requisitionDate' => $formDTO->getRequisitionDate(),
                $ekuaibaoPropertyService->getPaymentTypePropertyName() => $formDTO->getPaymentType(),
                'payeeId' => $formDTO->getPayeeId(),
                'description' => $formDTO->getDescription(),
                'details' => $details,
                $ekuaibaoPropertyService->getExpectedInvoiceDatePropertyName() => $formDTO->getExpectedInvoiceDate(),
            ]
        ];
        return $this->createFlow($params);
    }

    /**
     * 创建预付单-核销单据
     * @param PrepaymentReconciliationFlowFormDTO $formDTO
     * @return mixed
     * @throws CurlException
     */
    public function createPrepaymentReconciliationFlow(PrepaymentReconciliationFlowFormDTO $formDTO)
    {
        $details = [];
        foreach ($formDTO->getDetails() as $detail) {
            $details[] = $detail->toCreateFlowArray();
        }
        $loanWrittenOffs = [];
        foreach ($formDTO->getLoanWrittenOff() as $loanWrittenOffDTO) {
            $loanWrittenOffs[] = $loanWrittenOffDTO->toArray();
        }
        $ekuaibaoPropertyService = app(EkuaibaoPropertyService::class);
        $params = [
            'form' => [
                'specificationId' => $formDTO->getSpecificationId(),
                'title' => $formDTO->getTitle(),
                $ekuaibaoPropertyService->getLegalEntityPropertyName() => $formDTO->getLegalEntity(),
                'submitterId' => $formDTO->getSubmitterId(),
                'expenseDepartment' => $formDTO->getExpenseDepartment(),
                $ekuaibaoPropertyService->getDepartmentAttributePropertyName() => $formDTO->getDepartmentAttribute(),
                'payeeId' => $formDTO->getPayeeId(),
                'description' => $formDTO->getDescription(),
                'details' => $details,
            ],
            'params' => [
                'loanWrittenOff' => $loanWrittenOffs,
            ],
        ];
        return $this->createFlow($params);
    }
}