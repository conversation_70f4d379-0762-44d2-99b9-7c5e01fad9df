<?php

namespace App\Services\Ekuaibao\EkuaibaoBusiness;

use App\Entities\ProviderMerchantPayment\CreateFlowProcessorEntity;
use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Services\ProviderMerchantPayment\Common\EkuaibaoFlow\FlowProcessorFactory;
use Yanqu\YanquPhplib\Exception\CurlException;

class EkuaibaoService
{
    /**
     * @throws BusinessException
     * @throws BusinessWithoutErrorReportException
     * @throws CurlException
     */
    public function createEkuaibaoFlow($params)
    {
        $createFlowProcessorEntity = new CreateFlowProcessorEntity();
        $createFlowProcessorEntity->setApplicationType($params['application_type'] ?? 0);
        $createFlowProcessorEntity->setFlowType($params['flow_type'] ?? 0);
        $createFlowProcessorEntity->setCrmAccountId($params['crm_account_id'] ?? 0);
        $createFlowProcessorEntity->setOrderSettleId($params['order_settle_id'] ?? 0);
        $createFlowProcessorEntity->setReconciliationId($params['reconciliation_id'] ?? 0);
        $processor = app(FlowProcessorFactory::class)::createFlowProcessor($createFlowProcessorEntity);
        $processor->process();
    }
}