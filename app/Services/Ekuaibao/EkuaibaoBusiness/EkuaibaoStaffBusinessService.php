<?php

namespace App\Services\Ekuaibao\EkuaibaoBusiness;

use App\Constants\Ekuaibao\Staff\StaffTypeConstants;
use App\Entities\Ekuaibao\Staff\EkuaibaoStaffDTO;
use App\Models\CrmAccount;
use App\Repositories\CrmAccount\CrmAccountRepository;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoStaffService;
use Yanqu\YanquPhplib\Exception\CurlException;

class EkuaibaoStaffBusinessService
{
    /**
     * @throws CurlException
     */
    public function getStaffDTOByCrmAccountId($crmAccountId): EkuaibaoStaffDTO
    {
        /** @var CrmAccount $crmAccount */
        $crmAccount = app(CrmAccountRepository::class)->getCrmAccountByCrmAccountId($crmAccountId);
        if(empty($crmAccount)){
            return new EkuaibaoStaffDTO();
        }
        $ekuaibaoStaffService = app(EkuaibaoStaffService::class);
        $ekuaibaoStaffs = $ekuaibaoStaffService->getStaffs(StaffTypeConstants::USER_ID, [$crmAccount->mobile]);
        if (empty($ekuaibaoStaffs)) {
            $ekuaibaoStaffs = $ekuaibaoStaffService->getStaffs(StaffTypeConstants::CELLPHONE, [$crmAccount->mobile]);
        }
        if (empty($ekuaibaoStaffs)) {
            $ekuaibaoStaffs = $ekuaibaoStaffService->getStaffs(StaffTypeConstants::MAIL, [$crmAccount->loginame]);
        }
        return $ekuaibaoStaffService->buildStaffDTOByStaffData($ekuaibaoStaffs[0] ?? []);
    }
}