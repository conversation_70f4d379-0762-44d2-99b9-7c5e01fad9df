<?php

namespace App\Services\Ekuaibao\EkuaibaoBusiness;

use App\Entities\Ekuaibao\Attachment\AttachmentDTO;
use App\Entities\Ekuaibao\InvoiceForm\InvoiceFormDTO;
use App\Exceptions\BusinessException;
use Yanqu\YanquPhplib\Exception\CurlException;

class EkuaibaoInvoiceBusinessService
{
    /**
     * @param array $invoiceDTOs
     * @return InvoiceFormDTO
     * @throws BusinessException
     * @throws CurlException
     */
    public function getInvoiceFormDTOByProviderMerchantInvoices(array $invoiceDTOs = []): InvoiceFormDTO
    {
        $invoiceFormDTO = new InvoiceFormDTO();
        $invoiceFormDTO->setType('exist');
        $attachments = app(EkuaibaoAttachmentBusinessService::class)
            ->uploadAndGetAttachmentsByProviderMerchantInvoiceDTOs($invoiceDTOs);
        if (!empty($attachments)) {
            $attachments = array_map(function ($attachment) {
                return new AttachmentDTO($attachment);
            }, $attachments);
        } else {
            $attachments = [];
        }
        $invoiceFormDTO->setAttachments($attachments);
        return $invoiceFormDTO;
    }

    public function getInvoiceFormDTOForWait(): InvoiceFormDTO
    {
        $invoiceFormDTO = new InvoiceFormDTO();
        $invoiceFormDTO->setType('wait');
        return $invoiceFormDTO;
    }
}