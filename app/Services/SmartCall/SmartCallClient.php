<?php

namespace App\Services\SmartCall;

use App\Utils\SmartCall\SmartCallAuth;
use App\Utils\SmartCall\SmartCallAESUtil;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\YqLog\YqLog;

/**
 * 智能外呼系统客户端
 */
class SmartCallClient
{
    /**
     * 基础URL
     */
    private string $baseUrl;
    
    /**
     * 应用Key
     */
    private string $appKey;
    
    /**
     * 应用Secret
     */
    private string $appSecret;
    
    /**
     * 应用类型（外部短信渠道标签）
     */
    private string $appType;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->baseUrl = config('constants.smart_call_url');
        $this->appKey = config('constants.smart_call_app_key');
        $this->appSecret = config('constants.smart_call_app_secret');
        $this->appType = config('constants.smart_call_app_type', '');
        
        if (empty($this->baseUrl) || empty($this->appKey) || empty($this->appSecret)) {
            throw new \Exception('智能外呼系统配置不完整');
        }
    }
    
    /**
     * 发送GET请求
     *
     * @param string $endpoint 接口端点
     * @param array $params 请求参数
     * @param array $options 额外选项
     * @return array 响应数据
     * @throws \Exception
     */
    public function get(string $endpoint, array $params = [], array $options = []): array
    {
        $url = $this->baseUrl . $endpoint;
        
        // 生成请求查询字符串
        $requestQuery = SmartCallAuth::getRequestQueryStr($params);
        
        // 生成请求头
        $headers = SmartCallAuth::generateHeaders($requestQuery, '', $this->appKey, $this->appSecret, $this->appType);
        
        // 记录请求日志
        YqLog::logger('smart_call:request')->info('GET请求', [
            'url' => $url,
            'params' => $params,
            'headers' => $this->maskSensitiveHeaders($headers)
        ]);
        
        // 发送请求
        $response = CurlUtil::get($url, $params, $this->formatHeaders($headers), $options);
        
        return $this->handleResponse($response, $url, 'GET');
    }
    
    /**
     * 发送POST请求
     *
     * @param string $endpoint 接口端点
     * @param array $data 请求数据
     * @param array $options 额外选项
     * @return array 响应数据
     * @throws \Exception
     */
    public function post(string $endpoint, array $data = [], array $options = []): array
    {
        $url = $this->baseUrl . $endpoint;
        
        // 将数据转换为JSON字符串
        $requestBody = json_encode($data, JSON_UNESCAPED_UNICODE);
        
        // 生成请求头
        $headers = SmartCallAuth::generateHeaders('', $requestBody, $this->appKey, $this->appSecret, $this->appType);
        
        // 添加Content-Type
        $headers['Content-Type'] = 'application/json';
        
        // 记录请求日志
        YqLog::logger('smart_call:request')->info('POST请求', [
            'url' => $url,
            'data' => $data,
            'headers' => $this->maskSensitiveHeaders($headers)
        ]);
        
        // 发送请求
        $response = CurlUtil::postJson($url, $data, $this->formatHeaders($headers), $options);
        
        return $this->handleResponse($response, $url, 'POST');
    }
    
    /**
     * 处理响应
     *
     * @param string $response 原始响应
     * @param string $url 请求URL
     * @param string $method 请求方法
     * @return array 解析后的响应数据
     * @throws \Exception
     */
    private function handleResponse(string $response, string $url, string $method): array
    {
        // 记录响应日志
        YqLog::logger('smart_call:response')->info('响应数据', [
            'url' => $url,
            'method' => $method,
            'response' => $response
        ]);
        
        $responseData = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('响应数据格式错误: ' . json_last_error_msg());
        }
        
        // 检查响应状态
        if (!isset($responseData['code'])) {
            throw new \Exception('响应数据缺少状态码');
        }
        
        // 检查是否成功
        if ($responseData['code'] !== 200) {
            $errorMsg = $responseData['msg'] ?? '未知错误';
            
            // 记录错误日志
            YqLog::logger('smart_call:error')->error('API调用失败', [
                'url' => $url,
                'method' => $method,
                'code' => $responseData['code'],
                'message' => $errorMsg,
                'response' => $responseData
            ]);
            
            throw new \Exception("智能外呼API调用失败: {$errorMsg} (错误码: {$responseData['code']})");
        }
        
        return $responseData;
    }
    
    /**
     * 格式化请求头为CurlUtil所需的格式
     *
     * @param array $headers 请求头数组
     * @return array 格式化后的请求头
     */
    private function formatHeaders(array $headers): array
    {
        $formattedHeaders = [];
        foreach ($headers as $key => $value) {
            $formattedHeaders[] = "{$key}: {$value}";
        }
        return $formattedHeaders;
    }
    
    /**
     * 屏蔽敏感信息的请求头（用于日志记录）
     *
     * @param array $headers 原始请求头
     * @return array 屏蔽敏感信息后的请求头
     */
    private function maskSensitiveHeaders(array $headers): array
    {
        $maskedHeaders = $headers;
        if (isset($maskedHeaders['X-YS-SIGNATURE'])) {
            $maskedHeaders['X-YS-SIGNATURE'] = '***';
        }
        return $maskedHeaders;
    }
    
    /**
     * 加密敏感字段
     *
     * @param string $data 待加密数据
     * @param string $encryptKey 加密密钥（16位）
     * @return string 加密后的数据
     * @throws \Exception
     */
    public function encryptSensitiveField(string $data, string $encryptKey): string
    {
        return SmartCallAESUtil::encrypt($data, $encryptKey);
    }
    
    /**
     * 解密敏感字段
     *
     * @param string $encryptedData 加密的数据
     * @param string $encryptKey 解密密钥（16位）
     * @return string 解密后的数据
     * @throws \Exception
     */
    public function decryptSensitiveField(string $encryptedData, string $encryptKey): string
    {
        return SmartCallAESUtil::decrypt($encryptedData, $encryptKey);
    }
    
    /**
     * 获取MD5哈希值
     *
     * @param string $data 待哈希数据
     * @return string MD5哈希值
     */
    public function getMd5Hash(string $data): string
    {
        return SmartCallAESUtil::md5Encrypt($data);
    }
}
