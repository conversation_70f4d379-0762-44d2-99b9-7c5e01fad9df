<?php

namespace App\Services\SmartCall;

use App\Services\SmartCall\SmartCallClient;
use Yanqu\YanquPhplib\YqLog\YqLog;

/**
 * 智能外呼系统服务类
 * 提供具体的业务功能接口
 */
class SmartCallService
{
    /**
     * 智能外呼客户端
     */
    private SmartCallClient $client;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->client = new SmartCallClient();
    }
    
    /**
     * 向任务中导入客户信息
     * 注意：此接口有频率限制
     * - 每秒钟不能调用超过100次
     * - 每十分钟的调用总次数不能超过30000次
     *
     * @param array $customerData 客户数据
     * @param string|null $encryptKey 加密密钥（如果需要加密敏感字段）
     * @return array 导入结果
     * @throws \Exception
     */
    public function importCustomers(array $customerData, ?string $encryptKey = null): array
    {
        try {
            // 如果提供了加密密钥，对敏感字段进行加密
            if ($encryptKey && isset($customerData['phone'])) {
                $customerData['phone'] = $this->client->encryptSensitiveField($customerData['phone'], $encryptKey);
            }
            
            // 调用导入客户接口
            $response = $this->client->post('/customer/import', $customerData);
            
            YqLog::logger('smart_call:import_customers')->info('导入客户成功', [
                'customer_count' => count($customerData),
                'request_id' => $response['requestId'] ?? null
            ]);
            
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:import_customers')->error('导入客户失败', [
                'error' => $e->getMessage(),
                'customer_data' => $customerData
            ]);
            throw $e;
        }
    }
    
    /**
     * 创建外呼任务
     *
     * @param array $taskData 任务数据
     * @return array 创建结果
     * @throws \Exception
     */
    public function createCallTask(array $taskData): array
    {
        try {
            $response = $this->client->post('/task/create', $taskData);
            
            YqLog::logger('smart_call:create_task')->info('创建外呼任务成功', [
                'task_data' => $taskData,
                'request_id' => $response['requestId'] ?? null
            ]);
            
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:create_task')->error('创建外呼任务失败', [
                'error' => $e->getMessage(),
                'task_data' => $taskData
            ]);
            throw $e;
        }
    }
    
    /**
     * 查询任务状态
     *
     * @param string $taskId 任务ID
     * @return array 任务状态信息
     * @throws \Exception
     */
    public function getTaskStatus(string $taskId): array
    {
        try {
            $response = $this->client->get('/task/status', ['task_id' => $taskId]);
            
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_task_status')->error('查询任务状态失败', [
                'error' => $e->getMessage(),
                'task_id' => $taskId
            ]);
            throw $e;
        }
    }
    
    /**
     * 查询外呼结果
     *
     * @param string $taskId 任务ID
     * @param array $filters 过滤条件
     * @return array 外呼结果
     * @throws \Exception
     */
    public function getCallResults(string $taskId, array $filters = []): array
    {
        try {
            $params = array_merge(['task_id' => $taskId], $filters);
            $response = $this->client->get('/call/results', $params);
            
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_call_results')->error('查询外呼结果失败', [
                'error' => $e->getMessage(),
                'task_id' => $taskId,
                'filters' => $filters
            ]);
            throw $e;
        }
    }
    
    /**
     * 暂停外呼任务
     *
     * @param string $taskId 任务ID
     * @return array 操作结果
     * @throws \Exception
     */
    public function pauseTask(string $taskId): array
    {
        try {
            $response = $this->client->post('/task/pause', ['task_id' => $taskId]);
            
            YqLog::logger('smart_call:pause_task')->info('暂停外呼任务成功', [
                'task_id' => $taskId,
                'request_id' => $response['requestId'] ?? null
            ]);
            
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:pause_task')->error('暂停外呼任务失败', [
                'error' => $e->getMessage(),
                'task_id' => $taskId
            ]);
            throw $e;
        }
    }
    
    /**
     * 恢复外呼任务
     *
     * @param string $taskId 任务ID
     * @return array 操作结果
     * @throws \Exception
     */
    public function resumeTask(string $taskId): array
    {
        try {
            $response = $this->client->post('/task/resume', ['task_id' => $taskId]);
            
            YqLog::logger('smart_call:resume_task')->info('恢复外呼任务成功', [
                'task_id' => $taskId,
                'request_id' => $response['requestId'] ?? null
            ]);
            
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:resume_task')->error('恢复外呼任务失败', [
                'error' => $e->getMessage(),
                'task_id' => $taskId
            ]);
            throw $e;
        }
    }
    
    /**
     * 停止外呼任务
     *
     * @param string $taskId 任务ID
     * @return array 操作结果
     * @throws \Exception
     */
    public function stopTask(string $taskId): array
    {
        try {
            $response = $this->client->post('/task/stop', ['task_id' => $taskId]);
            
            YqLog::logger('smart_call:stop_task')->info('停止外呼任务成功', [
                'task_id' => $taskId,
                'request_id' => $response['requestId'] ?? null
            ]);
            
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:stop_task')->error('停止外呼任务失败', [
                'error' => $e->getMessage(),
                'task_id' => $taskId
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取通话录音
     *
     * @param string $callId 通话ID
     * @return array 录音信息
     * @throws \Exception
     */
    public function getCallRecording(string $callId): array
    {
        try {
            $response = $this->client->get('/call/recording', ['call_id' => $callId]);
            
            return $response['data'] ?? [];
            
        } catch (\Exception $e) {
            YqLog::logger('smart_call:get_recording')->error('获取通话录音失败', [
                'error' => $e->getMessage(),
                'call_id' => $callId
            ]);
            throw $e;
        }
    }
    
    /**
     * 批量导入客户（支持大批量数据）
     *
     * @param array $customers 客户列表
     * @param string|null $encryptKey 加密密钥
     * @param int $batchSize 批次大小
     * @return array 导入结果统计
     * @throws \Exception
     */
    public function batchImportCustomers(array $customers, ?string $encryptKey = null, int $batchSize = 100): array
    {
        $totalCount = count($customers);
        $successCount = 0;
        $failedCount = 0;
        $errors = [];
        
        // 分批处理
        $batches = array_chunk($customers, $batchSize);
        
        foreach ($batches as $batchIndex => $batch) {
            try {
                // 添加延迟以避免频率限制
                if ($batchIndex > 0) {
                    usleep(100000); // 延迟100毫秒
                }
                
                $this->importCustomers($batch, $encryptKey);
                $successCount += count($batch);
                
            } catch (\Exception $e) {
                $failedCount += count($batch);
                $errors[] = [
                    'batch' => $batchIndex + 1,
                    'error' => $e->getMessage(),
                    'count' => count($batch)
                ];
                
                YqLog::logger('smart_call:batch_import')->warning('批次导入失败', [
                    'batch_index' => $batchIndex + 1,
                    'batch_size' => count($batch),
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        $result = [
            'total_count' => $totalCount,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'errors' => $errors
        ];
        
        YqLog::logger('smart_call:batch_import')->info('批量导入完成', $result);
        
        return $result;
    }
}
