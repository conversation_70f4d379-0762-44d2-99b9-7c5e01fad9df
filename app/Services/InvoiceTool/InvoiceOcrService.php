<?php

namespace App\Services\InvoiceTool;

use App\Constants\Invoice\InvoiceTypeConstants;
use App\Constants\InvoiceTool\InvoiceOcrConstants;
use App\Entities\InvoiceTool\InvoiceOcrDTO;
use App\Exceptions\BusinessException;
use App\Repositories\InvoiceTool\InvoiceOcrRepository;
use Yanqu\YanquPhplib\Exception\CurlException;

class InvoiceOcrService
{
    /**
     * 发票OCR
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws CurlException
     */
    public function invoiceOcrAndSave($params): array
    {
        if(empty($params['file_url'])){
            return [];
        }
        $fileUrl = $params['file_url'];
        $invoiceOcrDTO = new InvoiceOcrDTO([]);
        $invoiceOcrDTO->setFileUrl($fileUrl);
        $invoiceOcrDTO = $this->invoiceOcr($invoiceOcrDTO, InvoiceOcrConstants::API_PROVIDER_BAIDU);
        $this->insertInvoiceOcr($invoiceOcrDTO);
        $invoiceTypeNameMap = InvoiceTypeConstants::INVOICE_TYPE_NAME_MAP;
        return [
            'file_url' => $invoiceOcrDTO->getFileUrl(),
            'invoice_type' => $invoiceOcrDTO->getInvoiceType(),
            'invoice_type_name' => $invoiceTypeNameMap[$invoiceOcrDTO->getInvoiceType()] ?? '',
            'invoice_number' => $invoiceOcrDTO->getInvoiceNumber(),
            'invoice_code' => $invoiceOcrDTO->getInvoiceCode(),
            'buyer_name' => $invoiceOcrDTO->getBuyerName(),
            'seller_name' => $invoiceOcrDTO->getSellerName(),
            'tax_inclusive_amount' => $invoiceOcrDTO->getTaxInclusiveAmount(),
            'tax_exclusive_amount' => $invoiceOcrDTO->getTaxExclusiveAmount(),
            'invoice_time' => $invoiceOcrDTO->getInvoiceTime(),
            'tax_rate' => $invoiceOcrDTO->getTaxRate(),
            'invoice_validation_code' => $invoiceOcrDTO->getInvoiceValidationCode(),
            'buyer_tax_number' => $invoiceOcrDTO->getBuyerTaxNumber(),
        ];
    }

    /**
     * @param InvoiceOcrDTO $invoiceOcrDTO
     * @param $apiProvider
     * @return InvoiceOcrDTO
     * @throws BusinessException
     * @throws CurlException
     */
    private function invoiceOcr(InvoiceOcrDTO $invoiceOcrDTO, $apiProvider): InvoiceOcrDTO
    {
        $thirdPartyOcrService = InvoiceOcrServiceFactory::create($apiProvider);
        $requestParams = $thirdPartyOcrService->buildRequestParams($invoiceOcrDTO);
        $response = $thirdPartyOcrService->request($requestParams);
        $invoiceOcrDTO->setOcrResult($response);
        return $thirdPartyOcrService->parseResponse($invoiceOcrDTO);
    }

    private function insertInvoiceOcr(InvoiceOcrDTO $invoiceOcrDTO)
    {
        $invoiceOcrRepository = new InvoiceOcrRepository();
        $invoiceOcr = [
            'invoice_file_url' => $invoiceOcrDTO->getFileUrl(),
            'ocr_result' => $invoiceOcrDTO->getOcrResult(),
        ];
        $invoiceOcrRepository->insertInvoiceOcr($invoiceOcr);
    }
}