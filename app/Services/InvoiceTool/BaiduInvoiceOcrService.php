<?php

namespace App\Services\InvoiceTool;

use App\Constants\InvoiceTool\InvoiceOcrConstants;
use App\Entities\InvoiceTool\InvoiceOcrDTO;
use App\Exceptions\BusinessException;
use App\Utils\CommonUtil;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\YqLog\YqLog;

class BaiduInvoiceOcrService implements ThirdPartyInvoiceOcrService
{
    /**
     * @param InvoiceOcrDTO $invoiceOcrDTO
     * @return array
     * @throws BusinessException
     */
    public function buildRequestParams(InvoiceOcrDTO $invoiceOcrDTO): array
    {
        $suffix = pathinfo($invoiceOcrDTO->getFileUrl(), PATHINFO_EXTENSION);
        switch ($suffix) {
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'bmp':
                $params = [
                    'image' => base64_encode(file_get_contents($invoiceOcrDTO->getFileUrl())),
                ];
                break;
            case 'pdf':
                $params = [
                    'pdf_file' => base64_encode(file_get_contents($invoiceOcrDTO->getFileUrl())),
                ];
                break;
            case 'ofd':
                $params = [
                    'ofd_file' => base64_encode(file_get_contents($invoiceOcrDTO->getFileUrl())),
                ];
                break;
            default:
                throw new BusinessException('Unsupported file format');
        }
        return $params;
    }

    /**
     * @param $params
     * @return string
     * @throws CurlException
     */
    public function request($params): string
    {
        $baiduInvoiceService = new BaiduInvoiceService();
        $token = $baiduInvoiceService->getBaiduAccessToken();
        $url = 'https://aip.baidubce.com/rest/2.0/ocr/v1/vat_invoice?access_token=' . $token;
        $response = CurlUtil::postForm($url, $params);
        YqLog::logger('finance:invoice:invoiceOcr')->info('BaiduOcrService request', [
            'params' => $params,
            'response' => $response,
        ]);
        return $response;
    }

    public function parseResponse(InvoiceOcrDTO $invoiceOcrDTO): InvoiceOcrDTO
    {
        $response = json_decode($invoiceOcrDTO->getOcrResult(), true);
        if (!isset($response['words_result'])) {
            YqLog::logger('finance:invoice:invoiceOcr')
                ->error('BaiduOcrService parseResponse error', [
                    'response' => $response,
                ]);
            return $invoiceOcrDTO;
        }
        $result = $response['words_result'];
        $invoiceOcrDTO->setInvoiceType($this->parseInvoiceType($result['InvoiceType'] ?? ''));
        $invoiceOcrDTO->setInvoiceNumber($result['InvoiceNum'] ?? '');
        $invoiceOcrDTO->setInvoiceCode($result['InvoiceCode'] ?? '');
        $invoiceOcrDTO->setBuyerName($result['PurchaserName'] ?? '');
        $invoiceOcrDTO->setSellerName($result['SellerName'] ?? '');
        $taxExclusiveAmount = !empty($result['TotalAmount']) && is_numeric($result['TotalAmount'])
            ? (float)$result['TotalAmount'] : 0.00;
        $taxAmount = !empty($result['TotalTax']) && is_numeric($result['TotalTax'])
            ? (float)$result['TotalTax'] : 0.00;
        $invoiceOcrDTO->setTaxInclusiveAmount((string)round($taxExclusiveAmount + $taxAmount, 2));
        $invoiceOcrDTO->setTaxExclusiveAmount($taxExclusiveAmount);
        $invoiceDate = $result['InvoiceDate'] ?? '';
        $invoiceOcrDTO->setInvoiceTime(CommonUtil::convertDateToYmd($invoiceDate));
        $taxRate = $result['CommodityTaxRate'][0]['word'] ?? '';
        $invoiceOcrDTO->setTaxRate(((int)$taxRate) / 100);
        $invoiceOcrDTO->setInvoiceValidationCode($result['CheckCode'] ?? '');
        $invoiceOcrDTO->setOcrResult(json_encode($result));
        $invoiceOcrDTO->setBuyerTaxNumber($result['PurchaserRegisterNum'] ?? '');
        return $invoiceOcrDTO;
    }

    private function parseInvoiceType($invoiceType)
    {
        $baiduInvoiceOcrApiInvoiceTypeMap = InvoiceOcrConstants::BAIDU_INVOICE_OCR_API_INVOICE_TYPE_MAP;
        $invoiceType = array_search($invoiceType, $baiduInvoiceOcrApiInvoiceTypeMap);
        return $invoiceType !== false ? $invoiceType : '';
    }
}