<?php

namespace App\Services\ProviderMerchantPayment\OrderSettle\Ekuaibao;

use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants as ReconciliationConstants;
use App\Entities\Ekuaibao\Detail\FormDetailDTO;
use App\Entities\Ekuaibao\FlowForm\PrepaymentReconciliationFlowFormDTO;
use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Models\ProviderMerchantInvoiceReconciliationApplication as Reconciliation;
use App\Repositories\ProviderMerchant\ProviderMerchantRepository;
use App\Repositories\ProviderMerchantPayment\InvoiceReconciliationApplicationRepository as ReconciliationRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFeeTypeService;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFlowService;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoMoneyService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoFlowBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoInvoiceBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoRecordLinkBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoStaffBusinessService;
use App\Services\ProviderMerchantPayment\Common\EkuaibaoFlow\CreateFlowProcessorInterface;
use App\Services\ProviderMerchantPayment\ProviderMerchantPaymentService;
use App\Services\ProviderMerchantPayment\SettleInfo\SettleInfoService;
use Illuminate\Database\Eloquent\Collection;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\EkuaibaoFlowCreatorConstants;

class CreateReconciliationFlowProcessor extends BaseCreateFlowProcessor implements CreateFlowProcessorInterface
{
    /**
     * @throws BusinessWithoutErrorReportException
     * @throws BusinessException
     * @throws CurlException
     */
    public function process()
    {
        $this->step = EkuaibaoFlowCreatorConstants::STEP_CREATE_SETTLEMENT_RECONCILIATION;
        //校验当前账号信息
        $providerMerchantPaymentService = new ProviderMerchantPaymentService();
        $providerMerchantPaymentService->checkCrmAccount($this->crmAccountId);

        //设置invoiceDTOs
        $this->invoiceDTOs = $this->loadInvoiceDTOs($this->step, $this->reconciliationId);

        //获取核销信息
        $reconciliationRepository = app(ReconciliationRepository::class);
        $reconciliation = $reconciliationRepository->getReconciliationApplicationById($this->reconciliationId);

        //校验核销信息
        $this->checkReconciliation($reconciliation);

        //获取关联的结算申请
        $settleIds = $reconciliationRepository
            ->getPaymentRelationsByReconciliationId($this->reconciliationId)
            ->where('relation_type', ReconciliationConstants::RELATE_TO_PAYMENT_TYPE_SETTLEMENT)
            ->pluck('related_application_id')->toArray();
        //获取结算信息
        $orderSettles = app(OrderSettleRepository::class)->getOrderSettlesByIds($settleIds);

        //获取供应商信息
        $providerMerchants = app(ProviderMerchantRepository::class)
            ->getProviderMerchantsByProviderMerchantIds($orderSettles->pluck('supplierid')->toArray());

        //构建表单
        $reconciliationFlowFormDTO = $this->buildReconciliationFlowFormDTO($reconciliation, $orderSettles,
            $providerMerchants);

        //创建单据
        $creatFlowResult = app(EkuaibaoFlowService::class)
            ->createPrepaymentReconciliationFlow($reconciliationFlowFormDTO);

        $this->handleAndSaveCreateFlowResult($creatFlowResult);
    }

    /**
     * @throws BusinessException
     * @throws CurlException
     * @throws BusinessWithoutErrorReportException
     */
    public function buildReconciliationFlowFormDTO(
        Reconciliation $reconciliation,
        Collection     $orderSettles,
        Collection     $providerMerchants
    ): PrepaymentReconciliationFlowFormDTO
    {
        //准备数据
        $ekuaibaoFlowBusinessService = app(EkuaibaoFlowBusinessService::class);
        $ekuaibaoStaffBusinessService = app(EkuaibaoStaffBusinessService::class);
        $ekuaibaoRecordLinkBusinessService = app(EkuaibaoRecordLinkBusinessService::class);
        $specificationId = $ekuaibaoFlowBusinessService->getPrepaymentReconciliationSpecificationId();
        $providerMerchantContactNames = $providerMerchants->pluck('contacter')->toArray();
        $title = sprintf(ReconciliationConstants::EKUAIBAO_FLOW_TITLE, '结算', $reconciliation->id,
            implode('、', $providerMerchantContactNames));
        $ekuaibaoStaffDTO = $ekuaibaoStaffBusinessService->getStaffDTOByCrmAccountId($this->crmAccountId);
        $departmentAttribute = $ekuaibaoRecordLinkBusinessService->getDepartmentAttributeIdByDepartmentId(
            $ekuaibaoStaffDTO->getDefaultDepartment());
        $payeeInfo = $this->getPayeeInfo($reconciliation->settle_infos_json);
        $payeeId = $payeeInfo->getId();
        $description = ReconciliationConstants::EKUAIBAO_FLOW_DESCRIPTION;
        $formDetailDTO = $this->getReconciliationFormDetailDTO($reconciliation);
        $loanWrittenOffs = $this->getLoadWrittenOff($orderSettles->pluck('ekuaibao_flow_id')->toArray(),
            $reconciliation->reconciliation_amount);

        //校验表单
        $settleInfo = json_decode($reconciliation->settle_infos_json, true);
        $settleInfoDTO = app(SettleInfoService::class)->buildSettleInfoDTO($settleInfo);
        $this->checkFlowFormInfo($payeeInfo, $settleInfoDTO, $ekuaibaoStaffDTO);

        //构建表单
        $prepaymentReconciliationFlowFormDTO = new PrepaymentReconciliationFlowFormDTO;
        $prepaymentReconciliationFlowFormDTO->setSpecificationId($specificationId);
        $prepaymentReconciliationFlowFormDTO->setTitle($title);
        $prepaymentReconciliationFlowFormDTO->setLegalEntity($reconciliation->ekuaibao_company_code);
        $prepaymentReconciliationFlowFormDTO->setSubmitterId($ekuaibaoStaffDTO->getId());
        $prepaymentReconciliationFlowFormDTO->setExpenseDepartment($ekuaibaoStaffDTO->getDefaultDepartment());
        $prepaymentReconciliationFlowFormDTO->setDepartmentAttribute($departmentAttribute);
        $prepaymentReconciliationFlowFormDTO->setPayeeId($payeeId);
        $prepaymentReconciliationFlowFormDTO->setDescription($description);
        $prepaymentReconciliationFlowFormDTO->setDetails([$formDetailDTO]);
        $prepaymentReconciliationFlowFormDTO->setLoanWrittenOff($loanWrittenOffs);
        return $prepaymentReconciliationFlowFormDTO;
    }

    /**
     * @throws BusinessException
     * @throws CurlException
     */
    public function getReconciliationFormDetailDTO(Reconciliation $reconciliation): FormDetailDTO
    {
        $ekuaibaoFeeTypeService = app(EkuaibaoFeeTypeService::class);
        $feeTypeId = $ekuaibaoFeeTypeService->getCostTestPlatformId();
        $feTypeSpecificationId = $ekuaibaoFeeTypeService->getExpenseSpecificationId();
        $invoiceForm = app(EkuaibaoInvoiceBusinessService::class)
            ->getInvoiceFormDTOByProviderMerchantInvoices($this->invoiceDTOs);
        $feeTypeFormAmount = app(EkuaibaoMoneyService::class)
            ->buildMoneyDTOByRmbAmount($reconciliation->reconciliation_amount);
        return $this->getFormDetailDTO($feeTypeFormAmount, $feeTypeId, $feTypeSpecificationId, $invoiceForm);
    }
}