<?php

namespace App\Services\ProviderMerchantPayment\OrderSettle\Ekuaibao;

use App\Constants\Ekuaibao\CustomDimension\EkuaibaoCustomDimensionConstants;
use App\Entities\Ekuaibao\Detail\FormDetailDTO;
use App\Entities\Ekuaibao\FlowForm\PrepaymentFowFormDTO;
use App\Entities\Ekuaibao\InvoiceForm\InvoiceFormDTO;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Models\OrderSettle;
use App\Models\ProviderMerchant;
use App\Repositories\ProviderMerchant\ProviderMerchantRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantSettleInfoRepository;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFeeTypeService;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFlowService;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoMoneyService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoCustomDimensionBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoFlowBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoInvoiceBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoStaffBusinessService;
use App\Services\ProviderMerchantPayment\Common\EkuaibaoFlow\CreateFlowProcessorInterface;
use App\Services\ProviderMerchantPayment\ProviderMerchantPaymentService;
use App\Services\ProviderMerchantPayment\SettleInfo\SettleInfoService;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\EkuaibaoFlowCreatorConstants;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\OrderSettleConstants;
use Yanqu\YanquPhplib\Utils\MoneyCalculatorUtil;

class CreatePrepaymentFlowProcessor extends BaseCreateFlowProcessor implements CreateFlowProcessorInterface
{
    /**
     * @throws CurlException
     * @throws BusinessWithoutErrorReportException
     */
    public function process()
    {
        $this->step = EkuaibaoFlowCreatorConstants::STEP_AUDIT_SETTLEMENT_BY_SUPPLIER_SPECIALIST;
        //校验当前账号信息
        $providerMerchantPaymentService = new ProviderMerchantPaymentService();
        $providerMerchantPaymentService->checkCrmAccount($this->crmAccountId);

        //获取结算信息
        $orderSettleRepository = app(OrderSettleRepository::class);
        $orderSettle = $orderSettleRepository->getOrderSettleBySettleId($this->orderSettleId);

        //校验结算申请
        $this->checkOrderSettle($orderSettle);

        //获取供应商信息
        /** @var ProviderMerchant $providerMerchant */
        $providerMerchant = app(ProviderMerchantRepository::class)
            ->getProviderMerchantByProviderMerchantId($orderSettle->supplierid);

        $prepaymentFlowFormDTO = $this->buildPrepaymentFlowFromDTO($orderSettle, $providerMerchant);
        //创建单据
        $createFlowResult = app(EkuaibaoFlowService::class)->createPrepaymentFlow($prepaymentFlowFormDTO);

        $this->handleAndSaveCreateFlowResult($createFlowResult);
    }

    /**
     * @throws CurlException
     * @throws BusinessWithoutErrorReportException
     */
    public function buildPrepaymentFlowFromDTO(
        OrderSettle      $orderSettle,
        ProviderMerchant $providerMerchant): PrepaymentFowFormDTO
    {
        //准备数据
        $ekuaibaoStaffBusinessService = app(EkuaibaoStaffBusinessService::class);
        $customDimensionBusinessService = app(EkuaibaoCustomDimensionBusinessService::class);
        $ekuaibaoFlowBusinessService = app(EkuaibaoFlowBusinessService::class);
        $specificationId = $ekuaibaoFlowBusinessService->getPrepaymentSpecificationId();
        $title = sprintf(OrderSettleConstants::EKUAIBAO_PREPAYMENT_FLOW_FORM_TITLE, $orderSettle->settleid,
            $providerMerchant->contacter);
        $ekuaibaoStaffDTO = $ekuaibaoStaffBusinessService->getStaffDTOByCrmAccountId($this->crmAccountId);
        $requisitionDate = strtotime(date('Y-m-d')) * 1000;
        $paymentType = $customDimensionBusinessService
            ->getPaymentTypeItemIdByItemName(
                EkuaibaoCustomDimensionConstants::PAYMENT_TYPE_ITEM_NAME_SUPPLIER_SETTLEMENT);
        $payeeInfo = $this->getPayeeInfo($orderSettle->acceptbankinfo);
        $payeeId = $payeeInfo->getId();
        $description = OrderSettleConstants::EKUAIBAO_CORPORATE_PAYMENT_FLOW_FORM_DESCRIPTION;
        $formDetailDTO = $this->getPrepaymentFlowFormDetailDTO($orderSettle);
        $expectedInvoiceDate = strtotime(date('Y-m', strtotime('+1 month'))) * 1000;

        //校验表单
        $settleInfo = json_decode($orderSettle->acceptbankinfo, true);
        $settleInfoDTO = app(SettleInfoService::class)->buildSettleInfoDTO($settleInfo);
        $this->checkFlowFormInfo($payeeInfo, $settleInfoDTO, $ekuaibaoStaffDTO);

        //构建表单
        $prepaymentFlowFormDTO = new PrepaymentFowFormDTO();
        $prepaymentFlowFormDTO->setSpecificationId($specificationId);
        $prepaymentFlowFormDTO->setTitle($title);
        $prepaymentFlowFormDTO->setLegalEntity(OrderSettleConstants::EKUAIBAO_LEGAL_ENTITY);
        $prepaymentFlowFormDTO->setSubmitterId($ekuaibaoStaffDTO->getId());
        $prepaymentFlowFormDTO->setExpenseDepartment($ekuaibaoStaffDTO->getDefaultDepartment());
        $prepaymentFlowFormDTO->setRequisitionDate($requisitionDate);
        $prepaymentFlowFormDTO->setPaymentType($paymentType);
        $prepaymentFlowFormDTO->setPayeeId($payeeId);
        $prepaymentFlowFormDTO->setDescription($description);
        $prepaymentFlowFormDTO->setDetails([$formDetailDTO]);
        $prepaymentFlowFormDTO->setExpectedInvoiceDate($expectedInvoiceDate);
        return $prepaymentFlowFormDTO;
    }

    /**
     * @throws CurlException
     */
    private function getPrepaymentFlowFormDetailDTO(OrderSettle $orderSettle): FormDetailDTO
    {
        $ekuaibaoFeeTypeService = app(EkuaibaoFeeTypeService::class);
        $feeTypeId = $ekuaibaoFeeTypeService->getCostTestPlatformId();
        $feTypeSpecificationId = $ekuaibaoFeeTypeService->getRequisitionSpecificationId();
        $invoiceForm = app(EkuaibaoInvoiceBusinessService::class)->getInvoiceFormDTOForWait();
        $currentPayableAmount = MoneyCalculatorUtil::amountSub($orderSettle->applyamt, $orderSettle->prestore);
        $feeTypeFormAmount = app(EkuaibaoMoneyService::class)->buildMoneyDTOByRmbAmount($currentPayableAmount);
        return $this->getFormDetailDTO($feeTypeFormAmount, $feeTypeId, $feTypeSpecificationId, $invoiceForm);
    }
}