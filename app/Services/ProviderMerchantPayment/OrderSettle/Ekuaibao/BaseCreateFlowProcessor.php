<?php

namespace App\Services\ProviderMerchantPayment\OrderSettle\Ekuaibao;

use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants as ReconciliationConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantPaymentErrorLogConstants;
use App\Constants\ProviderMerchantPayment\SettleInfoConstants;
use App\Entities\Ekuaibao\PayeeInfoDTO;
use App\Entities\Ekuaibao\Staff\EkuaibaoStaffDTO;
use App\Entities\ProviderMerchantPayment\ProviderMerchantInvoiceDTO;
use App\Entities\ProviderMerchantPayment\SettleInfoDTO;
use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Models\OrderSettle;
use App\Models\ProviderMerchantInvoiceReconciliationApplication as ReconciliationApplication;
use App\Repositories\ProviderMerchantPayment\InvoiceReconciliationApplicationRepository as ReconciliationRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPaymentErrorLogRepository;
use App\Services\ProviderMerchantPayment\EkuaibaoFlow\BaseEkuaibaoFlowProcessor;
use App\Services\ProviderMerchantPayment\SettleInfo\SettleInfoService;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\EkuaibaoFlowCreatorConstants;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\OrderSettleDealStatusConstants;
use Yanqu\YanquPhplib\YqLog\YqLog;

class BaseCreateFlowProcessor extends BaseEkuaibaoFlowProcessor
{
    protected $crmAccountId;
    protected $orderSettleId;
    protected $reconciliationId;
    /**
     * @var ProviderMerchantInvoiceDTO[] 发票
     */
    protected $invoiceDTOs;
    protected $step;

    /**
     * @throws BusinessWithoutErrorReportException
     */
    public function __construct($data)
    {
        if (empty($data['crmAccountId'])) {
            throw new BusinessWithoutErrorReportException('crmAccountId是必填的');
        }
        if (empty($data['orderSettleId']) && empty($data['reconciliationId'])) {
            throw new BusinessWithoutErrorReportException('orderSettleId或reconciliationId必填一个');
        }
        $this->crmAccountId = $data['crmAccountId'];
        $this->orderSettleId = $data['orderSettleId'] ?? 0;
        $this->reconciliationId = $data['reconciliationId'] ?? 0;
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    public function checkOrderSettle(OrderSettle $orderSettle = null)
    {
        if (empty($orderSettle)) {
            throw new BusinessWithoutErrorReportException(MessageConstants::ORDER_SETTLE_NOT_EXIT);
        }
        if ($this->step == EkuaibaoFlowCreatorConstants::STEP_AUDIT_SETTLEMENT_BY_SUPPLIER_SPECIALIST) {
            //已经创建过单据不能再次创建
            if (!empty($orderSettle->ekuaibao_flow_code) || !empty($orderSettle->ekuaibao_flow_id)) {
                throw new BusinessWithoutErrorReportException(MessageConstants::EKUAIBAO_FLOW_CODE_EXIST);
            }
            //非已提交状态的结算申请不能创建单据
            if ($orderSettle->dealstatus != OrderSettleDealStatusConstants::WAIT_SETTLE) {
                $statusName = OrderSettleDealStatusConstants::STATUSES;
                $statusName = $statusName[$orderSettle->dealstatus] ?? '';
                throw new BusinessWithoutErrorReportException(sprintf(
                    MessageConstants::ORDER_SETTLE_STATUS_NOT_ALLOW_TO_CREATE_EKUAIBAO_FLOW, $statusName));
            }
        }
        $this->checkSettleInfo(json_decode($orderSettle->acceptbankinfo, true));
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    private function checkSettleInfo($settleInfo)
    {
        $settleInfoService = new SettleInfoService();
        $settleInfoDTO = $settleInfoService->buildSettleInfoDTO($settleInfo);
        if ($settleInfoDTO->getPaymentMethod() != SettleInfoConstants::PAYMENT_METHOD_BANK_CARD) {
            $errorMessage = MessageConstants::SETTLE_INFO_NOT_BANK_CARD;
            $this->saveErrorLog($errorMessage);
            throw new BusinessWithoutErrorReportException($errorMessage);
        }
        if ($settleInfoDTO->getPaymentMode() != SettleInfoConstants::PAYMENT_MODE_CORPORATE) {
            $errorMessage = MessageConstants::SETTLE_INFO_NOT_CORPORATE;
            $this->saveErrorLog($errorMessage);
            throw new BusinessWithoutErrorReportException($errorMessage);
        }
    }

    /**
     * @throws BusinessException
     */
    public function checkReconciliation(ReconciliationApplication $reconciliation = null, $type = 0)
    {
        //校验核销信息
        if (empty($reconciliation)) {
            throw new BusinessException(MessageConstants::RECONCILIATION_NOT_EXIST);
        }
        //已经创建过单据不能再次创建
        if (!empty($reconciliation->ekuaibao_flow_code)) {
            throw new BusinessException(MessageConstants::EKUAIBAO_FLOW_CODE_EXIST);
        }
        if ($reconciliation->process_status != ReconciliationConstants::PROCESS_STATUS_TO_BE_REVIEWED) {
            throw new BusinessException(MessageConstants::RECONCILIATION_STATUS_NOT_ALLOW);
        }
        if ($type == ReconciliationConstants::RECONCILE_TYPE_PREPAYMENT &&
            $reconciliation->reconciliation_type != ReconciliationConstants::RECONCILE_TYPE_PREPAYMENT) {
            throw new BusinessException(MessageConstants::RECONCILIATION_NOT_PREPAYMENT);
        } elseif ($type == ReconciliationConstants::RECONCILE_TYPE_SETTLEMENT &&
            $reconciliation->reconciliation_type != ReconciliationConstants::RECONCILE_TYPE_SETTLEMENT) {
            throw new BusinessException(MessageConstants::RECONCILIATION_NOT_SETTLEMENT);
        }
    }

    public function saveErrorLog($content)
    {
        YqLog::logger("finance:providerMerchantPayment:ekuaibaoFlow")->warning($content, [
            'orderSettleId' => $this->orderSettleId,
            'crmAccountId' => $this->crmAccountId,
            'creatFlowResult' => $content,
            'reconciliationId' => $this->reconciliationId,
            'step' => $this->step,
        ]);

        $isOrderSettle = $this->step == EkuaibaoFlowCreatorConstants::STEP_AUDIT_SETTLEMENT_BY_SUPPLIER_SPECIALIST;
        $errorLog = [
            'application_type' => $isOrderSettle ?
                ProviderMerchantPaymentErrorLogConstants::APPLICATION_TYPE_SETTLEMENT :
                ProviderMerchantPaymentErrorLogConstants::APPLICATION_TYPE_INVOICE_RECONCILIATION,
            'related_application_id' => $isOrderSettle ? $this->orderSettleId : $this->reconciliationId,
            'crm_account_id' => $this->crmAccountId,
            'error_content' => $content,
            'step' => $isOrderSettle ?
                ProviderMerchantPaymentErrorLogConstants::STEP_AUDIT_SETTLEMENT_BY_SUPPLIER_SPECIALIST :
                ProviderMerchantPaymentErrorLogConstants::STEP_CREATE_RECONCILIATION,
        ];
        app(ProviderMerchantPaymentErrorLogRepository::class)->insertErrorLog($errorLog);
    }

    public function handleAndSaveCreateFlowResult($creatFlowResult)
    {
        if (isset($creatFlowResult['flow']['id']) && isset($creatFlowResult['flow']['form']['code'])) {
            //更新单据编号和易快报单据操作人
            $flowFormCode = $creatFlowResult['flow']['form']['code'];
            $flowId = $creatFlowResult['flow']['id'];
            $this->updateEkuaibaoFlowAndOperator($flowId, $flowFormCode);
        } else {
            $errorMessage = is_array($creatFlowResult) ? json_encode($creatFlowResult) : (string)$creatFlowResult;
            $this->saveErrorLog($errorMessage);
        }
    }

    public function updateEkuaibaoFlowAndOperator($flowId, $flowFormCode)
    {
        if ($this->step == EkuaibaoFlowCreatorConstants::STEP_AUDIT_SETTLEMENT_BY_SUPPLIER_SPECIALIST) {
            app(OrderSettleRepository::class)->updateEkuaibaoFlowAndOperator($this->orderSettleId,
                $flowFormCode, $flowId, env('CRM_ACCOUNT_ID_SYSTEM'));
        } elseif ($this->step == EkuaibaoFlowCreatorConstants::STEP_CREATE_SETTLEMENT_RECONCILIATION) {
            app(ReconciliationRepository::class)->updateEkuaibaoFlowAndOperator($this->reconciliationId,
                $flowFormCode, $flowId, env('CRM_ACCOUNT_ID_SYSTEM'));
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    public function checkFlowFormInfo(
        PayeeInfoDTO     $payeeInfo,
        SettleInfoDTO    $settleInfoDTO,
        EkuaibaoStaffDTO $ekuaibaoStaffDTO): void
    {
        if (empty($ekuaibaoStaffDTO->getId())) {
            $errorMessage = MessageConstants::EKUAIBAO_STAFF_NOT_CONFIGURED;
            $this->saveErrorLog($errorMessage);
            throw new BusinessWithoutErrorReportException($errorMessage);
        }
        if (empty($payeeInfo->getName())) {
            $errorMessage = MessageConstants::EKUAIBAO_PAYEE_INFO_NOT_CONFIGURED;
            $this->saveErrorLog($errorMessage);
            throw new BusinessWithoutErrorReportException($errorMessage);
        }
        if ($payeeInfo->getName() != $settleInfoDTO->getBankName()) {
            $errorMessage = sprintf(MessageConstants::EKUAIBAO_PAYEE_NAME_NOT_EQUAL,
                $payeeInfo->getName(), $settleInfoDTO->getBankName());
            $this->saveErrorLog($errorMessage);
            throw new BusinessWithoutErrorReportException($errorMessage);
        }
    }
}