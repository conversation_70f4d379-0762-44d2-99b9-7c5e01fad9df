<?php

namespace App\Services\ProviderMerchantPayment\OrderSettle\Ekuaibao;

use App\Constants\Ekuaibao\CustomDimension\EkuaibaoCustomDimensionConstants;
use App\Entities\Ekuaibao\Detail\FormDetailDTO;
use App\Entities\Ekuaibao\FlowForm\CorporatePaymentFlowFormDTO;
use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Models\OrderSettle;
use App\Models\ProviderMerchant;
use App\Repositories\ProviderMerchant\ProviderMerchantRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantSettleInfoRepository;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFeeTypeService;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFlowService;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoMoneyService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoCustomDimensionBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoFlowBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoInvoiceBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoRecordLinkBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoStaffBusinessService;
use App\Services\ProviderMerchantPayment\Common\EkuaibaoFlow\CreateFlowProcessorInterface;
use App\Services\ProviderMerchantPayment\ProviderMerchantPaymentService;
use App\Services\ProviderMerchantPayment\SettleInfo\SettleInfoService;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\EkuaibaoFlowCreatorConstants;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\OrderSettleConstants;

class CreateCorporatePaymentFlowProcessor extends BaseCreateFlowProcessor implements CreateFlowProcessorInterface
{
    /**
     * @throws BusinessWithoutErrorReportException
     * @throws CurlException
     * @throws BusinessException
     */
    public function process()
    {
        $this->step = EkuaibaoFlowCreatorConstants::STEP_AUDIT_SETTLEMENT_BY_SUPPLIER_SPECIALIST;
        //校验当前账号信息
        $providerMerchantPaymentService = new ProviderMerchantPaymentService();
        $providerMerchantPaymentService->checkCrmAccount($this->crmAccountId);

        //设置invoiceDTOs
        $this->invoiceDTOs = $this->loadInvoiceDTOs($this->step, $this->orderSettleId);

        //获取结算信息
        $orderSettleRepository = app(OrderSettleRepository::class);
        $orderSettle = $orderSettleRepository->getOrderSettleBySettleId($this->orderSettleId);

        //收款账户实时查询
        $payeeAccountInfo = app(ProviderMerchantSettleInfoRepository::class)
            ->getSettleInfoById($orderSettle->merchantsettleinfoid);
        $orderSettle->acceptbankinfo = json_encode($payeeAccountInfo->toArray(), JSON_UNESCAPED_UNICODE);

        //校验结算申请
        $this->checkOrderSettle($orderSettle);

        //获取供应商信息
        /** @var ProviderMerchant $providerMerchant */
        $providerMerchant = app(ProviderMerchantRepository::class)
            ->getProviderMerchantByProviderMerchantId($orderSettle->supplierid);

        //构建表单
        $corporatePaymentFlowFormDTO = $this->buildCorporatePaymentFlowFormDTO($orderSettle, $providerMerchant);

        //创建单据
        $creatFlowResult = app(EkuaibaoFlowService::class)->createCorporatePaymentFlow($corporatePaymentFlowFormDTO);

        $this->handleAndSaveCreateFlowResult($creatFlowResult);
    }

    /**
     * @throws CurlException
     * @throws BusinessException
     * @throws BusinessWithoutErrorReportException
     */
    private function buildCorporatePaymentFlowFormDTO(
        OrderSettle      $orderSettle,
        ProviderMerchant $providerMerchant
    ): CorporatePaymentFlowFormDTO
    {
        //准备数据
        $ekuaibaoStaffBusinessService = app(EkuaibaoStaffBusinessService::class);
        $customDimensionBusinessService = app(EkuaibaoCustomDimensionBusinessService::class);
        $ekuaibaoFlowService = app(EkuaibaoFlowBusinessService::class);
        $ekuaibaoRecordLinkBusinessService = app(EkuaibaoRecordLinkBusinessService::class);
        $specificationId = $ekuaibaoFlowService->getCorporatePaymentSpecificationId();
        $title = sprintf(OrderSettleConstants::EKUAIBAO_CORPORATE_PAYMENT_FLOW_FORM_TITLE,
            $orderSettle->settleid, $providerMerchant->contacter);
        $ekuaibaoStaffDTO = $ekuaibaoStaffBusinessService->getStaffDTOByCrmAccountId($this->crmAccountId);
        $departmentAttribute = $ekuaibaoRecordLinkBusinessService->getDepartmentAttributeIdByDepartmentId(
            $ekuaibaoStaffDTO->getDefaultDepartment());
        $paymentType = $customDimensionBusinessService
            ->getPaymentTypeItemIdByItemName(
                EkuaibaoCustomDimensionConstants::PAYMENT_TYPE_ITEM_NAME_SUPPLIER_SETTLEMENT);
        $payeeInfo = $this->getPayeeInfo($orderSettle->acceptbankinfo);
        $payeeId = $payeeInfo->getId();
        $description = OrderSettleConstants::EKUAIBAO_CORPORATE_PAYMENT_FLOW_FORM_DESCRIPTION;
        $formDetailDTO = $this->getCorporatePaymentFlowFormDetailDTO($orderSettle, $this->invoiceDTOs);
        $isFinalPayment = $customDimensionBusinessService
            ->getYesOrNoItemIdByItemName(EkuaibaoCustomDimensionConstants::YES_OR_NO_ITEM_NAME_NO);
        $archiveFlag = $customDimensionBusinessService
            ->getArchiveFlagItemIdByItemName(EkuaibaoCustomDimensionConstants::ARCHIVE_FLAG_ITEM_NAME_ARCHIVED);
        $callbackFlag = OrderSettleConstants::EKUAIBAO_CORPORATE_PAYMENT_FLOW_FORM_CALLBACK_FLAG;

        //校验表单
        $settleInfo = json_decode($orderSettle->acceptbankinfo, true);
        $settleInfoDTO = app(SettleInfoService::class)->buildSettleInfoDTO($settleInfo);
        $this->checkFlowFormInfo($payeeInfo, $settleInfoDTO, $ekuaibaoStaffDTO);

        //构建表单
        $corporatePaymentFlowFormDTO = new CorporatePaymentFlowFormDTO();
        $corporatePaymentFlowFormDTO->setSpecificationId($specificationId);
        $corporatePaymentFlowFormDTO->setTitle($title);
        $corporatePaymentFlowFormDTO->setLegalEntity(OrderSettleConstants::EKUAIBAO_LEGAL_ENTITY);
        $corporatePaymentFlowFormDTO->setSubmitterId($ekuaibaoStaffDTO->getId());
        $corporatePaymentFlowFormDTO->setSubmitterDepartment($ekuaibaoStaffDTO->getDefaultDepartment());
        $corporatePaymentFlowFormDTO->setExpenseDepartment($ekuaibaoStaffDTO->getDefaultDepartment());
        $corporatePaymentFlowFormDTO->setDepartmentAttribute($departmentAttribute);
        $corporatePaymentFlowFormDTO->setPaymentType($paymentType);
        $corporatePaymentFlowFormDTO->setPayeeId($payeeId);
        $corporatePaymentFlowFormDTO->setDescription($description);
        $corporatePaymentFlowFormDTO->setDetails([$formDetailDTO]);
        $corporatePaymentFlowFormDTO->setIsFinalPayment($isFinalPayment);
        $corporatePaymentFlowFormDTO->setParticipants($ekuaibaoStaffDTO->getId());
        $corporatePaymentFlowFormDTO->setArchiveFlag($archiveFlag);
        $corporatePaymentFlowFormDTO->setCallbackFlag($callbackFlag);
        $corporatePaymentFlowFormDTO->setContractQuantity(0);
        return $corporatePaymentFlowFormDTO;
    }

    /**
     * @throws BusinessException
     * @throws CurlException
     */
    public function getCorporatePaymentFlowFormDetailDTO(
        OrderSettle $orderSettle,
        array       $invoiceDTOs = []): FormDetailDTO
    {
        $ekuaibaoFeeTypeService = app(EkuaibaoFeeTypeService::class);
        $feeTypeId = $ekuaibaoFeeTypeService->getCostTestPlatformId();
        $feTypeSpecificationId = $ekuaibaoFeeTypeService->getExpenseSpecificationId();
        $invoiceForm = app(EkuaibaoInvoiceBusinessService::class)
            ->getInvoiceFormDTOByProviderMerchantInvoices($invoiceDTOs);
        $currentPayableAmount = round(bcsub($orderSettle->applyamt, $orderSettle->prestore, 4), 2);
        $feeTypeFormAmount = app(EkuaibaoMoneyService::class)->buildMoneyDTOByRmbAmount($currentPayableAmount);
        return $this->getFormDetailDTO($feeTypeFormAmount, $feeTypeId, $feTypeSpecificationId, $invoiceForm);
    }
}