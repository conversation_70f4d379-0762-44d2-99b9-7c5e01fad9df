<?php

namespace App\Services\ProviderMerchantPayment\OrderSettle;

use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants as ReconciliationConstants;
use App\Entities\ProviderMerchantPayment\CheckUploadInvoiceFieldsEntity;
use App\Entities\ProviderMerchantPayment\ProviderMerchantInvoiceDTO;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Models\OrderSettle;
use App\Repositories\Ekuaibao\EkuaibaoCompanyRepository;
use App\Repositories\ProviderMerchant\ProviderMerchantRepository;
use App\Repositories\ProviderMerchantPayment\InvoiceReconciliationApplicationRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantInvoiceRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantSettleInfoRepository;
use App\Services\ProviderMerchantPayment\Invoice\BaseSaveInvoicesService;
use App\Services\ProviderMerchantPayment\Invoice\ProviderMerchantInvoiceService;
use App\Services\ProviderMerchantPayment\ProviderMerchantPaymentService;
use App\Services\ProviderMerchantPayment\Reconciliation\ProviderMerchantInvoiceReconciliationService;
use App\Services\ProviderMerchantPayment\SettleInfo\SettleInfoService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\EkuaibaoFlowCreatorConstants;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\InvoicePaymentOrderConstants;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\InvoiceStatusConstants;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\OrderSettleConstants;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\OrderSettleDealStatusConstants;
use Yanqu\YanquPhplib\Queue\MNSClient;
use Yanqu\YanquPhplib\Utils\MoneyCalculatorUtil;

class SaveOrderSettleInvoicesService extends BaseSaveInvoicesService
{
    /**
     * @throws BusinessWithoutErrorReportException
     * @throws \Exception
     */
    public function saveOrderSettleInvoices($params)
    {
        //获取参数
        $settleIds = $params['order_settle_ids'] ?? [];
        $invoices = $params['invoices'] ?? [];
        $crmAccountId = $params['crm_account_id'] ?? 0;
        $providerMerchantId = $params['provider_merchant_id'] ?? 0;

        if (empty($settleIds)) {
            return;
        }

        //校验当前操作人
        $providerMerchantPaymentService = new ProviderMerchantPaymentService();
        if (!empty($crmAccountId)) {
            $providerMerchantPaymentService->checkCrmAccount($crmAccountId);
        } elseif (!empty($providerMerchantId)) {
            if (!app(OrderSettleService::class)
                ->isOrderSettleBelongsToProviderMerchant($settleIds, $providerMerchantId)) {
                throw new BusinessWithoutErrorReportException("结算申请不属于当前供应商");
            }
        } else {
            throw new BusinessWithoutErrorReportException('无法获取当前账号信息');
        }

        //校验结算申请和发票信息
        $this->checkBeforeSaveOrderSettleInvoices($settleIds, $invoices);

        DB::beginTransaction();

        //保存发票
        $invoiceService = new ProviderMerchantInvoiceService();
        $invoiceDTOs = $invoiceService->buildProviderMerchantInvoiceDTOs($invoices);
        $invoiceDTOs = $invoiceService->saveInvoices($invoiceDTOs);

        //保存发票和结算申请的关联关系
        $this->addInvoiceToOrderSettleRelation($settleIds, $invoiceDTOs);

        //保存结算申请的发票状态
        app(OrderSettleService::class)->saveOrderSettlesInvoiceStatus($settleIds);

        //生成核销申请
        $reconciliationId = $this->createReconciliationIfPaid($settleIds, $invoiceDTOs, $crmAccountId);

        //如果是专员上传的发票，更新状态为待财务审核
        if ($crmAccountId > 0 && $reconciliationId > 0) {
            app(InvoiceReconciliationApplicationRepository::class)->updateReconciliation($reconciliationId, [
                'process_status' => ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_TO_BE_REVIEWED,
            ]);
        }

        DB::commit();

        if ($reconciliationId > 0) {
            //发送创建核销单据的MNS消息
            MNSClient::getInstance()->sendTopicJsonMsg(
                env('MNS_COMMON_EVENT_TOPIC_NAME'),
                [
                    'application_type' => EkuaibaoFlowCreatorConstants::APPLICATION_TYPE_ORDER_SETTLE,
                    'flow_type' => EkuaibaoFlowCreatorConstants::FLOW_TYPE_RECONCILIATION,
                    'crm_account_id' => $crmAccountId,
                    'reconciliation_id' => $reconciliationId,
                ],
                EkuaibaoFlowCreatorConstants::MNS_MESSAGE_TAG
            );
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    private function checkBeforeSaveOrderSettleInvoices(array $settleIds, array $invoices)
    {
        $repo = new OrderSettleRepository();
        $settles = $repo->getOrderSettlesByIds($settleIds);
        $settleInfoJsons = $settles->pluck('acceptbankinfo');
        $settleInfoDTOs = [];
        foreach ($settleInfoJsons as $settleInfoJson){
            $settleInfo = json_decode($settleInfoJson, true);
            $settleInfoDTOs[] = app(SettleInfoService::class)->buildSettleInfoDTO($settleInfo);
        }

        //校验无效的结算申请
        $this->checkSettleCount($settles, $settleIds);
        $this->checkApplyTimes($settles);
        //校验供应商主体
        $this->checkProviderMerchantName($settles);
        //校验结算信息
        $this->checkSettleInfoFields($settleInfoDTOs);
        //校验付款方式
        $this->checkPaymentModes($settleInfoDTOs);
        //校验结算申请的状态
        $this->checkDealStatus($settles);
        //校验开票状态
        $this->checkInvoiceStatus($settles);

        //校验发票信息
        $checkUploadInvoiceFieldsEntity = new CheckUploadInvoiceFieldsEntity();
        $checkUploadInvoiceFieldsEntity->setCompanyName(app(EkuaibaoCompanyRepository::class)
            ->getCompanyNameByCode(OrderSettleConstants::EKUAIBAO_LEGAL_ENTITY));
        $providerMerchant = app(ProviderMerchantRepository::class)
            ->getProviderMerchantByProviderMerchantId($settles->first()->supplierid);
        $checkUploadInvoiceFieldsEntity->setProviderMerchantName($providerMerchant->merchantname);
        $checkUploadInvoiceFieldsEntity->setCompanyTaxNumber(OrderSettleConstants::EKUAIBAO_LEGAL_ENTITY_TAX_NUMBER);
        $invoiceService = new ProviderMerchantInvoiceService();
        $invoiceDTOs = $invoiceService->buildProviderMerchantInvoiceDTOs($invoices);
        $this->checkInvoicesSummary($invoiceDTOs, $settles);
        $this->checkUploadInvoiceFields($invoiceDTOs, $checkUploadInvoiceFieldsEntity);
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    private function checkSettleCount($settles, $settleIds): void
    {
        if ($settles->count() !== count($settleIds)) {
            throw new BusinessWithoutErrorReportException(MessageConstants::SETTLE_HAS_INVALID_SETTLE);
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    private function checkApplyTimes(Collection $settles)
    {
        $minApplyTimes = $settles->min('postime');
        if ($minApplyTimes < strtotime(OrderSettleConstants::INVOICE_UPLOAD_RELEASE_TIME)) {
            throw new BusinessWithoutErrorReportException(
                MessageConstants::UPLOAD_INVOICE_DISABLED_FOR_LEGACY_SETTLEMENT);
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    private function checkProviderMerchantName(Collection $settles)
    {
        $providerMerchantIds = $settles->pluck('supplierid');
        $providerMerchants = app(ProviderMerchantRepository::class)
            ->getProviderMerchantsByProviderMerchantIds($providerMerchantIds);
        $providerMerchantNames = $providerMerchants->pluck('merchantname')->unique();
        if ($providerMerchantNames->count() > 1) {
            throw new BusinessWithoutErrorReportException(MessageConstants::SETTLE_MERCHANT_NOT_SAME);
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    private function checkDealStatus(Collection $settles)
    {
        if ($settles->pluck('dealstatus')->unique()->count() > 1) {
            throw new BusinessWithoutErrorReportException(MessageConstants::SETTLE_STATUS_NOT_SAME);
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    private function checkInvoiceStatus(Collection $settles): void
    {
        $invoiceStatuses = $settles->pluck('invoice_status')->toArray();
        if (count(array_unique($invoiceStatuses)) > 1) {
            throw new BusinessWithoutErrorReportException(MessageConstants::SETTLE_INVOICE_STATUS_NOT_SAME);
        }
        $settleCount = count($settles);
        /** @var $settle OrderSettle */
        foreach ($settles as $settle) {
            if ((int)$settle->invoice_status === InvoiceStatusConstants::INVOICED) {
                throw new BusinessWithoutErrorReportException(
                    sprintf(MessageConstants::SETTLE_ALREADY_INVOICE, $settle->settleid));
            }
            if ($settle->invoice_status == InvoiceStatusConstants::PARTIAL_INVOICED &&
                $settleCount > 1) {
                throw new BusinessWithoutErrorReportException(
                    sprintf(MessageConstants::SETTLE_PARTIAL_INVOICE_CANNOT_UPLOAD_INVOICE_WITH_OTHERS,
                        $settle->settleid));
            }
            if($settle->invoice_status == InvoiceStatusConstants::NO_INVOICE_NEEDED){
                throw new BusinessWithoutErrorReportException(
                    sprintf(MessageConstants::SETTLE_NO_INVOICE_NEEDED, $settle->settleid));
            }
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    private function checkInvoicesSummary(array $invoiceDTOs, Collection $settles)
    {
        $invoiceAmountSum = 0;
        foreach ($invoiceDTOs as $invoiceDTO) {
            $invoiceAmountSum = MoneyCalculatorUtil::amountAdd($invoiceAmountSum, $invoiceDTO->getInvoiceAmount());
        }
        $relatedInvoiceAmountsMap = app(OrderSettleRepository::class)->getInvoiceRelatedAmountMapBySettleIds(
            $settles->pluck('settleid')->toArray())->toArray();
        $relatedInvoiceAmountSum = array_sum($relatedInvoiceAmountsMap);
        $applyAmountSum = $settles->sum('applyamt');
        $deductedPrepaymentAmountSum = $settles->sum('prestore');
        $unrelatedAmount = MoneyCalculatorUtil::amountSub($applyAmountSum, $deductedPrepaymentAmountSum);
        $unrelatedAmount = MoneyCalculatorUtil::amountSub($unrelatedAmount, $relatedInvoiceAmountSum);
        if ($settles->count() > 1) {
            if (count($invoiceDTOs) != 1) {
                throw new BusinessWithoutErrorReportException(MessageConstants::SETTLE_CAN_ONLY_UPLOAD_ONE_INVOICE);
            }
            if (!MoneyCalculatorUtil::isEqual($invoiceAmountSum, $unrelatedAmount)) {
                throw new BusinessWithoutErrorReportException(
                    sprintf(MessageConstants::SETTLE_INVOICE_AMOUNT_MISMATCH,
                        MoneyCalculatorUtil::amountRound($invoiceAmountSum),
                        MoneyCalculatorUtil::amountRound($unrelatedAmount)));
            }
        } else {
            if (MoneyCalculatorUtil::isGreaterThan($invoiceAmountSum, $unrelatedAmount)) {
                throw new BusinessWithoutErrorReportException(
                    MessageConstants::SETTLE_INVOICE_AMOUNT_MUST_LESS_THAN_OR_EQUAL_TO_PENDING_PAYMENT_AMOUNT);
            }
        }

    }

    /**
     * @param array $settleIds
     * @param ProviderMerchantInvoiceDTO[] $invoiceDTOs
     * @return void
     */
    private function addInvoiceToOrderSettleRelation(array $settleIds, array $invoiceDTOs)
    {
        $orderSettleRepo = app(OrderSettleRepository::class);
        $settles = $orderSettleRepo->getOrderSettlesByIds($settleIds)->keyBy('settleid');
        $relatedAmountMap = $orderSettleRepo->getInvoiceRelatedAmountMapBySettleIds($settleIds);
        $invoiceRepo = new ProviderMerchantInvoiceRepository();
        foreach ($invoiceDTOs as $invoiceDTO) {
            $unrelatedInvoiceAmount = $invoiceDTO->getInvoiceAmount();
            foreach ($settleIds as $settleId) {
                $settle = $settles[$settleId] ?? null;
                $currentPayableAmount = MoneyCalculatorUtil::amountSub($settle->applyamt, $settle->prestore);
                $relatedAmount = $relatedAmountMap[$settleId] ?? 0;
                $relateAmount = MoneyCalculatorUtil::amountSub($currentPayableAmount, $relatedAmount);
                $relateAmount = min($relateAmount, $unrelatedInvoiceAmount);
                $unrelatedInvoiceAmount = MoneyCalculatorUtil::amountSub($unrelatedInvoiceAmount, $relateAmount);
                $insert = [
                    'provider_merchant_invoice_id' => $invoiceDTO->getInvoiceId(),
                    'relation_type' => ReconciliationConstants::RELATE_TO_PAYMENT_TYPE_SETTLEMENT,
                    'related_application_id' => $settleId,
                    'relate_amount' => MoneyCalculatorUtil::amountRound($relateAmount),
                    'invoice_payment_order' => $settle->dealstatus == OrderSettleDealStatusConstants::SETTLED ?
                        InvoicePaymentOrderConstants::PAYMENT_FIRST :
                        InvoicePaymentOrderConstants::INVOICE_FIRST,
                ];
                $invoiceRepo->saveInvoiceToPaymentRelation($insert);
            }
        }
    }

    private function createReconciliationIfPaid(array $settleIds, array $invoiceDTOs, $crmAccountId = 0): int
    {
        $repo = new OrderSettleRepository();
        $firstSettle = $repo->getOrderSettleBySettleId($settleIds[0]);

        if ($firstSettle->invoice_payment_order == InvoicePaymentOrderConstants::PAYMENT_FIRST &&
            $firstSettle->dealstatus == OrderSettleDealStatusConstants::SETTLED) {
            return app(ProviderMerchantInvoiceReconciliationService::class)
                ->createReconciliationForOrderSettle($settleIds, $invoiceDTOs, $crmAccountId);
        }
        return 0;
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    public function checkInvoicesBeforeAddSettle(array $params)
    {
        $invoices = $params['invoices'] ?? [];
        $applyAmount = $params['apply_amount'] ?? 0;
        $prepaymentDeductedAmount = $params['prepayment_deducted_amount'] ?? 0;
        $providerMerchantId = $params['provider_merchant_id'] ?? 0;
        $settleAccountId = $params['settle_account_id'] ?? 0;

        if (empty($applyAmount) || empty($providerMerchantId)) {
            throw new BusinessWithoutErrorReportException('结算金额、供应商ID不能为空');
        }

        $currentPayableAmount = MoneyCalculatorUtil::amountSub($applyAmount, $prepaymentDeductedAmount);
        if (MoneyCalculatorUtil::isEqual($currentPayableAmount, 0)) {
            return;
        }

        //校验：先票后款必须上传发票
        $settleAccount = app(ProviderMerchantSettleInfoRepository::class)->getSettleInfoById($settleAccountId);
        if($settleAccount->winfksort == InvoicePaymentOrderConstants::INVOICE_FIRST && empty($invoices)){
            throw new BusinessWithoutErrorReportException('结算方式为先票后款时，必须上传发票');
        }

        if(empty($invoices)){
            return;
        }
        if ($settleAccount->providermerchantid != $providerMerchantId) {
            throw new BusinessWithoutErrorReportException('结算账号不属于当前供应商');
        }

        //校验付款方式
        $this->checkPaymentMode($settleAccount->paymentmode);

        // 发票DTO构建
        $invoiceService = new ProviderMerchantInvoiceService();
        $invoiceDTOs = $invoiceService->buildProviderMerchantInvoiceDTOs($invoices);

        // 发票总金额校验
        $invoiceAmountSum = array_reduce($invoiceDTOs, function ($sum, $dto) {
            return MoneyCalculatorUtil::amountAdd($sum, empty($dto->getInvoiceId()) ? $dto->getInvoiceAmount() : 0);
        }, 0);
        if (!MoneyCalculatorUtil::isEqual($invoiceAmountSum, $currentPayableAmount)) {
            throw new BusinessWithoutErrorReportException(
                sprintf(MessageConstants::SETTLE_INVOICE_AMOUNT_MISMATCH,
                    MoneyCalculatorUtil::amountRound($invoiceAmountSum),
                    MoneyCalculatorUtil::amountRound($currentPayableAmount)));
        }

        // 获取供应商信息
        $providerMerchant = app(ProviderMerchantRepository::class)
            ->getProviderMerchantByProviderMerchantId($providerMerchantId);
        if (empty($providerMerchant)) {
            throw new BusinessWithoutErrorReportException('供应商信息不存在');
        }

        // 获取签约主体名
        $companyId = OrderSettleConstants::EKUAIBAO_LEGAL_ENTITY;
        $ekuaibaoCompanyName = app(EkuaibaoCompanyRepository::class)->getCompanyNameByCode($companyId);

        // 构造发票字段校验参数
        $checkEntity = new CheckUploadInvoiceFieldsEntity();
        $checkEntity->setProviderMerchantName($providerMerchant->merchantname);
        $checkEntity->setCompanyName($ekuaibaoCompanyName);
        $checkEntity->setCompanyTaxNumber(OrderSettleConstants::EKUAIBAO_LEGAL_ENTITY_TAX_NUMBER);

        // 发票字段校验
        $this->checkUploadInvoiceFields($invoiceDTOs, $checkEntity);
    }
}
