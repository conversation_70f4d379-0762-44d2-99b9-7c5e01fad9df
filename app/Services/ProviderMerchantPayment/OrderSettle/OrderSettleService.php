<?php

namespace App\Services\ProviderMerchantPayment\OrderSettle;

use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Models\EkuaibaoCompany;
use App\Models\OrderSettle;
use App\Models\ProviderMerchant;
use App\Repositories\Ekuaibao\EkuaibaoCompanyRepository;
use App\Repositories\ProviderMerchant\ProviderMerchantRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFlowService;
use App\Services\ProviderMerchantPayment\ProviderMerchantPaymentService;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\InvoicePaymentOrderConstants;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\InvoiceStatusConstants;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\OrderSettleConstants;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\OrderSettle\OrderSettleDealStatusConstants;
use Yanqu\YanquPhplib\Utils\MoneyCalculatorUtil;

class OrderSettleService
{

    public function getOrderSettlesForUploadInvoice($params): array
    {
        $orderSettleRepository = app(OrderSettleRepository::class);
        $params['invoice_statuses'] = [
            InvoiceStatusConstants::NOT_INVOICED,
            InvoiceStatusConstants::PARTIAL_INVOICED,
        ];
        $params['deal_statuses'] = [OrderSettleDealStatusConstants::SETTLED];
        //限制一下上线前的历史数据不要被查询出来
        $params['min_apply_time'] = strtotime(OrderSettleConstants::INVOICE_UPLOAD_RELEASE_TIME);
        $params['invoice_payment_order'] = InvoicePaymentOrderConstants::PAYMENT_FIRST;
        $orderSettlesBuilder = $orderSettleRepository->getOrderSettlesBuilder($params);
        $count = $orderSettlesBuilder->count();
        $orderSettles = $orderSettlesBuilder
            ->select('settleid', 'application_number', 'supplierid', 'applyamt', 'postime', 'company_id',
                'prestore')
            ->orderBy('settleid', 'desc')
            ->paginate($params['page_size'] ?? 20);
        $providerMerchants = app(ProviderMerchantRepository::class)
            ->getProviderMerchantsByProviderMerchantIds($orderSettles->pluck('supplierid'))
            ->keyBy('providermerchantid');
        $ekuaibaoCompanies = app(EkuaibaoCompanyRepository::class)->getCompanies()->keyBy('code');
        $invoiceRelateAmountMap = $orderSettleRepository
            ->getInvoiceRelatedAmountMapBySettleIds($orderSettles->pluck('settleid')->toArray());
        $returnOrderSettles = [];
        foreach ($orderSettles as $orderSettle) {
            $providerMerchant = $providerMerchants[$orderSettle->supplierid] ?? null;
            $ekuaibaoCompany = $ekuaibaoCompanies[$orderSettle->company_id] ?? null;
            $invoiceRelatedAmount = $invoiceRelateAmountMap[$orderSettle->settleid] ?? 0;
            $returnOrderSettles[] = $this->buildOrderSettleForUploadInvoice(
                $orderSettle,
                $providerMerchant,
                $ekuaibaoCompany,
                $invoiceRelatedAmount
            );
        }
        return [
            'total' => $count,
            'list' => $returnOrderSettles,
            'page' => $params['page'] ?? 1,
        ];
    }

    private function buildOrderSettleForUploadInvoice(
        OrderSettle       $orderSettle,
        ?ProviderMerchant $providerMerchant,
        ?EkuaibaoCompany  $ekuaibaoCompany,
                          $invoiceRelatedAmount): array
    {
        $currentPayableAmount = MoneyCalculatorUtil::amountSub($orderSettle->applyamt, $orderSettle->prestore);
        $unRelatedAmount = MoneyCalculatorUtil::amountSub($currentPayableAmount, $invoiceRelatedAmount);
        return [
            'order_settle_id' => $orderSettle->settleid,
            'provider_merchant_contact_name' => $providerMerchant->contacter ?? '',
            'provider_merchant_name' => $providerMerchant->merchantname ?? '',
            'apply_amount' => $orderSettle->applyamt,
            'unrelated_invoice_amount' => MoneyCalculatorUtil::amountRound($unRelatedAmount),
            'ekuaibao_company_name' => $ekuaibaoCompany->company ?? '',
            'formated_apply_time' => !empty($orderSettle->postime) ? date('Y/m/d', $orderSettle->postime) : '',
        ];
    }

    /**
     * @param $params
     * @return void
     * @throws BusinessException
     * @throws BusinessWithoutErrorReportException
     * @throws CurlException
     */
    public function saveEkuaibaoFlowCode($params)
    {
        $ekuaibaoFlowCode = $params['ekuaibao_flow_code'];
        $orderSettleId = $params['order_settle_id'];
        $crmAccountId = $params['crm_account_id'];
        $providerMerchantPaymentService = new ProviderMerchantPaymentService();
        $providerMerchantPaymentService->checkCrmAccount($crmAccountId);

        //校验结算申请信息
        $orderSettleRepository = app(OrderSettleRepository::class);
        $orderSettle = $orderSettleRepository->getOrderSettleBySettleId($orderSettleId);
        if ($ekuaibaoFlowCode == $orderSettle->ekuaibao_flow_code) {
            return;
        }
        $this->checkOrderSettleBeforeSaveEkuaibaoFlowCode($orderSettle);

        //获取易快报单据信息
        $ekuaibaoFlowService = new EkuaibaoFlowService();
        $flowDetail = $ekuaibaoFlowService->getFlowDetailByFlowCode($ekuaibaoFlowCode);

        //校验易快报单据信息
        $this->checkFlowBeforeSaveEkuaibaoFlowCode($flowDetail, $orderSettle);

        //保存易快报单据号
        $flowId = $flowDetail['value']['id'] ?? '';
        $orderSettleRepository->updateEkuaibaoFlowAndOperator($orderSettleId, $ekuaibaoFlowCode, $flowId,
            $crmAccountId);
    }

    /**
     * @param OrderSettle|null $orderSettle
     * @return void
     * @throws BusinessWithoutErrorReportException
     */
    private function checkOrderSettleBeforeSaveEkuaibaoFlowCode(OrderSettle $orderSettle = null)
    {
        if (empty($orderSettle)) {
            throw new BusinessWithoutErrorReportException(MessageConstants::ORDER_SETTLE_NOT_EXIT);
        }
        if ($orderSettle->ekuaibao_flow_operator_crm_account_id == env('CRM_ACCOUNT_ID_SYSTEM')) {
            throw new BusinessWithoutErrorReportException(
                MessageConstants::ORDER_SETTLE_AUTO_PROCESS_CANNOT_SAVE_EKUAIBAO_FLOW_CODE);
        }
        if ($orderSettle->dealstatus != OrderSettleDealStatusConstants::WAIT_SETTLE) {
            $statusNames = OrderSettleDealStatusConstants::STATUSES;
            $statusName = $statusNames[$orderSettle->dealstatus] ?? '';
            throw new BusinessWithoutErrorReportException(sprintf(
                MessageConstants::ORDER_SETTLE_STATUS_NOT_ALLOW_TO_SAVE_EKUAIBAO_FLOW_CODE, $statusName));
        }
    }

    /**
     * @param $flowDetail
     * @param OrderSettle $orderSettle
     * @return void
     * @throws BusinessException|CurlException|BusinessWithoutErrorReportException
     */
    private function checkFlowBeforeSaveEkuaibaoFlowCode($flowDetail, OrderSettle $orderSettle)
    {
        if (empty($flowDetail)) {
            throw new BusinessWithoutErrorReportException(MessageConstants::EKUAIBAO_FLOW_NOT_EXIST);
        }
        //校验金额相等
        $payMoneyDTO = app(EkuaibaoFlowService::class)->buildPayMoneyDTOBYFlowDetail($flowDetail);
        $pendingPaymentAmount = round(bcsub($orderSettle->applyamt, $orderSettle->prestore, 4), 2);
        if ($pendingPaymentAmount == 0) {
            throw new BusinessWithoutErrorReportException(
                MessageConstants::PENDING_PAYMENT_AMOUNT_IS_ZERO_FOR_EKUAIBAO_FLOW_CODE);
        }
        if (bccomp($payMoneyDTO->getStandard(), $pendingPaymentAmount, 2) != 0) {
            throw new BusinessWithoutErrorReportException(sprintf(MessageConstants::EKUAIBAO_AMOUNT_NOT_EQUAL_TO_SETTLE,
                $payMoneyDTO->getStandard(), $pendingPaymentAmount));
        }

        //校验收款信息一致
        $providerMerchantPaymentService = new ProviderMerchantPaymentService();
        $providerMerchantPaymentService->checkFlowSettleInfoConsistency($flowDetail, $orderSettle->acceptbankinfo,
            $orderSettle->merchantsettleinfoid);

        //校验是否重复
        $providerMerchantPaymentService->checkDuplicateEkuaibaoFlow($flowDetail);
    }

    public function saveOrderSettlesInvoiceStatus(array $settleIds)
    {
        $amountsMap = $this->getSettlementWithRelatedInvoiceAmountMap($settleIds);
        $repository = new OrderSettleRepository();

        foreach ($settleIds as $settleId) {
            $amounts = $amountsMap[$settleId] ?? [];
            $invoiceStatus = $this->getInvoiceStatusByAmounts($amounts);

            $repository->updateInvoiceStatus($settleId, $invoiceStatus);
            $repository->updateInvoice($settleId, $invoiceStatus == InvoiceStatusConstants::INVOICED ?
                OrderSettleConstants::INVOICED : OrderSettleConstants::NOT_INVOICED);
        }
    }

    private function getInvoiceStatusByAmounts($amounts): int
    {
        //待打款金额
        $currentPayableAmount = $amounts['currentPayableAmount'] ?? '0.00';
        $relatedInvoiceAmount = $amounts['relatedInvoiceAmount'] ?? '0.00';

        if (MoneyCalculatorUtil::isEqual($currentPayableAmount, 0)) {
            $status = InvoiceStatusConstants::NO_INVOICE_NEEDED;
        } elseif (MoneyCalculatorUtil::isEqual($currentPayableAmount, $relatedInvoiceAmount)) {
            $status = InvoiceStatusConstants::INVOICED;
        } elseif (
            MoneyCalculatorUtil::isGreaterThan($currentPayableAmount, $relatedInvoiceAmount) &&
            MoneyCalculatorUtil::isGreaterThan($relatedInvoiceAmount, '0.00')
        ) {
            $status = InvoiceStatusConstants::PARTIAL_INVOICED;
        } else {
            $status = InvoiceStatusConstants::NOT_INVOICED;
        }
        return $status;
    }

    private function getSettlementWithRelatedInvoiceAmountMap(array $settleIds): array
    {
        $orderSettleRepository = app(OrderSettleRepository::class);
        $settles = $orderSettleRepository->getOrderSettlesByIds($settleIds);
        $relatedInvoiceAmountMap = $orderSettleRepository->getInvoiceRelatedAmountMapBySettleIds($settleIds);

        $map = [];
        foreach ($settles as $settle) {
            $settleId = $settle->settleid;
            $currentPayableAmount = MoneyCalculatorUtil::amountSub($settle->applyamt, $settle->prestore);
            $relatedInvoiceAmount = $relatedInvoiceAmountMap[$settleId] ?? '0.00';

            $map[$settleId] = [
                'currentPayableAmount' => MoneyCalculatorUtil::amountRound($currentPayableAmount),
                'relatedInvoiceAmount' => MoneyCalculatorUtil::amountRound($relatedInvoiceAmount),
            ];
        }
        return $map;
    }

    public function isOrderSettleBelongsToProviderMerchant($settleIds, $providerMerchantId): bool
    {
        $orderSettles = app(OrderSettleRepository::class)->getOrderSettlesByIds($settleIds);
        $supplierIds = $orderSettles->pluck('supplierid')->unique()->toArray();
        return !empty($supplierIds) &&
            (count($supplierIds) == 1 && $supplierIds[0] == $providerMerchantId);
    }
}