<?php

namespace App\Services\ProviderMerchantPayment\Prepayment;

use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantPrepaymentConstants;
use App\Entities\ProviderMerchantPayment\ProviderMerchantInvoiceDTO;
use App\Exceptions\BusinessException;
use App\Models\ProviderMerchantInvoice;
use App\Models\ProviderPrestore;
use App\Repositories\CrmAccount\CrmAccountRepository;
use App\Repositories\Ekuaibao\EkuaibaoCompanyRepository;
use App\Repositories\ProviderMerchant\ProviderMerchantRepository;
use App\Repositories\ProviderMerchantPayment\InvoiceReconciliationApplicationRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantInvoiceRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPrepaymentRepository;
use App\Services\ProviderMerchantPayment\EkuaibaoFlow\EkuaibaoPrepaymentReconciliationFlowProcessor;
use App\Services\ProviderMerchantPayment\Invoice\ProviderMerchantInvoiceService;
use App\Services\ProviderMerchantPayment\Reconciliation\ProviderMerchantInvoiceReconciliationService;
use App\Services\Supplier\ProviderMerchantService;
use App\Utils\StringHelper;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class SavePrepaymentInvoicesAndRelationsService
{
    private $crmAccountId;

    /**
     * @return mixed
     */
    public function getCrmAccountId()
    {
        return $this->crmAccountId;
    }

    /**
     * @param mixed $crmAccountId
     */
    public function setCrmAccountId($crmAccountId): void
    {
        $this->crmAccountId = $crmAccountId;
    }

    /**
     * 保存供应商预存发票及其关联关系前的校验
     * @param $params
     * @return void
     * @throws BusinessException
     */
    public function checkBeforeSavePrepaymentInvoicesAndRelations($params)
    {
        $prepaymentIds = $params['prepayment_ids'];
        $reconciliationApplicationId = $params['reconciliation_application_id'] ?? 0;
        $invoices = $params['invoices'];
        if (empty($prepaymentIds)) {
            return;
        }

        //获取预存申请列表
        $providerMerchantPrepaymentRepository = new ProviderMerchantPrepaymentRepository();
        $prepayments = $providerMerchantPrepaymentRepository->getPrepaymentsByPrepaymentIds($prepaymentIds);
        if (empty($prepayments) || $prepayments->count() != count($prepaymentIds)) {
            throw new BusinessException(MessageConstants::PREPAYMENT_NOT_EXIST);
        }

        //校验预存申请的供应商主体
        $providerMerchants = (app(ProviderMerchantRepository::class))
            ->getProviderMerchantsByProviderMerchantIds($prepayments->pluck('providermerchantid')->toArray());
        $this->checkPrepaymentProviderMerchantSubjectName($providerMerchants->pluck('merchantname')->toArray());

        //校验预存申请的状态
        $this->checkPrepaymentStatus($prepayments);

        //校验付款方式
        $this->checkPrepaymentPaymentMode($prepayments);

        //校验预存申请的开票状态
        $this->checkPrepaymentInvoiceStatus($prepayments);

        //校验预存申请的签约主体
        $this->checkPrepaymentEkuaibaoCompany($prepayments);

        //校验预存申请的结算信息
        $this->checkPrepaymentSettleInfo($prepayments);

        //校验核销申请的状态
        $this->checkReconciliationStatus($reconciliationApplicationId);

        //校验总发票信息
        $providerMerchantInvoiceService = new ProviderMerchantInvoiceService();
        $providerMerchantInvoiceDTOs = $providerMerchantInvoiceService->buildProviderMerchantInvoiceDTOs($invoices);
        $this->checkInvoicesSummary($prepayments, $providerMerchantInvoiceDTOs);

        //获取签约主体名称
        $ekuaibaoCompanyRepository = new EkuaibaoCompanyRepository();
        $ekuaibaoCompanyName = $ekuaibaoCompanyRepository->getCompanyNameByCode($prepayments->first()->company_id);
        //获取旧的相同发票
        $oldSameInvoices = $this->getOldSameInvoices($providerMerchantInvoiceDTOs);
        //校验单张发票信息
        $this->checkSingleInvoice($providerMerchantInvoiceDTOs, [
            'providerMerchantSubjectName' => $providerMerchants->first() ?
                $providerMerchants->first()->merchantname : "",
            'ekuaibaoCompanyName' => $ekuaibaoCompanyName,
            'oldSameInvoices' => $oldSameInvoices]);
    }

    /**
     * @param ProviderMerchantInvoiceDTO[] $providerMerchantInvoiceDTOs
     * @return Collection
     */
    private function getOldSameInvoices(array $providerMerchantInvoiceDTOs): Collection
    {
        $providerMerchantInvoiceRepository = new ProviderMerchantInvoiceRepository();
        $invoiceNumbers = array_map(function ($providerMerchantInvoiceDTO) {
            return $providerMerchantInvoiceDTO->getInvoiceNumber();
        }, $providerMerchantInvoiceDTOs);
        $sameInvoices = $providerMerchantInvoiceRepository->getInvoicesByInvoiceNumbers($invoiceNumbers);

        //排除未关联预存或结算的发票
        $sameInvoiceIds = $sameInvoices->pluck('id')->toArray();
        $relatedToPaymentInvoiceIds = $providerMerchantInvoiceRepository
            ->getRelatedToPaymentInvoiceIdsByInvoiceIds($sameInvoiceIds);
        return $sameInvoices->whereIn('id', $relatedToPaymentInvoiceIds);
    }

    /**
     * 校验预存申请的供应商主体
     * @param $providerMerchantSubjectNames
     * @return void
     * @throws BusinessException
     */
    private function checkPrepaymentProviderMerchantSubjectName($providerMerchantSubjectNames)
    {
        if (count($providerMerchantSubjectNames) > 1) {
            throw new BusinessException(MessageConstants::PREPAYMENT_MERCHANT_NOT_SAME);
        }
    }

    /**
     * 校验预存申请的状态
     * @param $prepayments
     * @return void
     * @throws BusinessException
     */
    private function checkPrepaymentStatus($prepayments)
    {
        $statuses = $prepayments->pluck('status')->unique();
        if ($statuses->count() > 1) {
            throw new BusinessException(MessageConstants::PREPAYMENT_STATUS_NOT_SAME);
        }
    }

    private function checkPrepaymentPaymentMode($prepayments)
    {
        $paymentModes = $prepayments->pluck('paymentmode')->unique();
        if ($paymentModes->count() > 1 ||
            $paymentModes->first() == ProviderMerchantPrepaymentConstants::PAYMENT_MODE_PERSONAL) {
            throw new BusinessException(MessageConstants::PREPAYMENT_PAYMENT_MODE_NOT_ALLOW_UPLOAD_INVOICE);
        }
    }

    /**
     * 校验预存申请的开票状态
     * @param $prepayments
     * @return void
     * @throws BusinessException
     */
    private function checkPrepaymentInvoiceStatus($prepayments)
    {
        $invoiceStatuses = $prepayments->pluck('invoice_status')->unique();
        if ($invoiceStatuses->count() > 1) {
            throw new BusinessException(MessageConstants::PREPAYMENT_INVOICE_STATUS_NOT_SAME);
        }
        $prepaymentCount = $prepayments->count();
        foreach ($prepayments as $prepayment) {
            if ($prepayment->invoice_status == ProviderMerchantPrepaymentConstants::INVOICE_STATUS_INVOICED) {
                throw new BusinessException(MessageConstants::PREPAYMENT_ALREADY_INVOICE);
            }
            if($prepayment->invoice_status == ProviderMerchantPrepaymentConstants::INVOICE_STATUS_PARTIAL_INVOICED &&
                $prepaymentCount > 1) {
                throw new BusinessException(
                    MessageConstants::PREPAYMENT_PARTIAL_INVOICE_CANNOT_UPLOAD_INVOICE_WITH_OTHERS);
            }
        }
    }

    /**
     * 校验预存申请的签约主体
     * @param $prepayments
     * @return void
     * @throws BusinessException
     */
    private function checkPrepaymentEkuaibaoCompany($prepayments)
    {
        $ekuaibaoCompanyIds = $prepayments->pluck('company_id')->unique();
        if ($ekuaibaoCompanyIds->count() > 1) {
            throw new BusinessException(MessageConstants::PREPAYMENT_SIGNATORY_NOT_SAME);
        }
    }

    /**
     * 校验预存申请的结算信息
     * @param $prepayments
     * @return void
     * @throws BusinessException
     */
    private function checkPrepaymentSettleInfo($prepayments)
    {
        $newLastSettleInfo = [];
        foreach ($prepayments as $key => $prepayment) {
            $settleInfo = json_decode($prepayment->settleinfojson, true);
            $oldLastSettleInfo = $newLastSettleInfo;
            $newLastSettleInfo = $settleInfo;
            //第一条数据不校验
            if ($key == 0) {
                continue;
            }
            if (empty($settleInfo)) {
                throw new BusinessException(MessageConstants::SETTLE_INFO_NOT_EMPTY);
            }
            $checkKeys = ['bankaccount'];
            foreach ($settleInfo as $settleKey => $settleValue) {
                if (!in_array($settleKey, $checkKeys)) {
                    continue;
                }
                if (($settleValue != null && !isset($oldLastSettleInfo[$settleKey])) ||
                    $oldLastSettleInfo[$settleKey] != $settleValue) {
                    throw new BusinessException(MessageConstants::SETTLE_INFO_NOT_SAME);
                }
            }
        }
    }

    /**
     * 校验核销申请的状态
     * @param $reconciliationApplicationId
     * @return void
     * @throws BusinessException
     */
    private function checkReconciliationStatus($reconciliationApplicationId)
    {
        if (!empty($reconciliationApplicationId)) {
            $invoiceReconciliationApplicationRepository = new InvoiceReconciliationApplicationRepository();
            $reconciliationApplication = $invoiceReconciliationApplicationRepository
                ->getReconciliationApplicationById($reconciliationApplicationId);
            if (empty($reconciliationApplication)) {
                throw new BusinessException(MessageConstants::RECONCILIATION_NOT_EXIST);
            }
            if ($reconciliationApplication->process_status !=
                ProviderMerchantInvoiceReconciliationConstants::PROCESS_STATUS_REJECTED) {
                throw new BusinessException(
                    MessageConstants::RECONCILIATION_STATUS_NOT_ALLOW_TO_REUPLOAD_INVOICE);
            }
        }
    }

    /**
     * 校验总发票信息
     * @param $prepayments
     * @param ProviderMerchantInvoiceDTO[] $providerMerchantInvoiceDTOs
     * @return void
     * @throws BusinessException
     */
    private function checkInvoicesSummary($prepayments, array $providerMerchantInvoiceDTOs)
    {
        $providerMerchantPrepaymentService = new ProviderMerchantPrepaymentService();
        //获取预存申请的待关联发票总金额
        $prepaymentAmountsMap = $providerMerchantPrepaymentService->getPrepaymentWithAmountsMapByPrepaymentIds(
            $prepayments->pluck('prestoreid')->toArray());
        $prepaymentUnrelatedInvoiceAmountSum = round(array_sum(array_column($prepaymentAmountsMap,
            'unRelatedInvoiceAmount')), 2);

        //获取发票总金额
        $newInvoiceAmountMap = array_map(function ($providerMerchantInvoiceDTO) {
            return empty($providerMerchantInvoiceDTO->getInvoiceId()) ?
                $providerMerchantInvoiceDTO->getInvoiceAmount() : 0;
        }, $providerMerchantInvoiceDTOs);
        $newInvoiceAmountSum = round(array_sum($newInvoiceAmountMap), 2);

        if ($prepayments->count() == 1 && bccomp($newInvoiceAmountSum, $prepaymentUnrelatedInvoiceAmountSum, 2) === 1) {
            //单笔预存申请，发票总金额必须小于等于预存总金额
            throw new BusinessException("发票总金额{$newInvoiceAmountSum}大于预存未上传发票金额" .
                "{$prepaymentUnrelatedInvoiceAmountSum}，请检查");
        } elseif ($prepayments->count() > 1 && bccomp($newInvoiceAmountSum, $prepaymentUnrelatedInvoiceAmountSum,
                2) !== 0) {
            //多笔预存申请，发票总金额必须等于预存总金额
            throw new BusinessException("发票总金额{$newInvoiceAmountSum}与预存总未上传发票金额" .
                "{$prepaymentUnrelatedInvoiceAmountSum}不一致，请检查");
        }

        if ($prepayments->count() > 1 && count($providerMerchantInvoiceDTOs) > 1) {
            throw new BusinessException("多笔预存只能上传一张发票，请检查");
        }
    }

    /**
     *
     * @param array{prepayment:array{amount:int, provider_merchant_id: int, company_id: int}, invoices:array{}} $params
     * @return void
     * @throws BusinessException
     */
    public function checkInvoicesBeforeAddPrepayment(array $params)
    {
        $invoices = $params['invoices'];
        if (empty($invoices)) {
            return;
        }

        //校验总发票信息
        $providerMerchantInvoiceService = new ProviderMerchantInvoiceService();
        $providerMerchantInvoiceDTOs = $providerMerchantInvoiceService->buildProviderMerchantInvoiceDTOs($invoices);
        //获取发票总金额
        $newInvoiceAmountMap = array_map(function ($providerMerchantInvoiceDTO) {
            return empty($providerMerchantInvoiceDTO->getInvoiceId()) ?
                $providerMerchantInvoiceDTO->getInvoiceAmount() : 0;
        }, $providerMerchantInvoiceDTOs);
        $newInvoiceAmountSum = round(array_sum($newInvoiceAmountMap), 2);

        $prepaymentAmount = $params['amount'];
        if (bccomp($newInvoiceAmountSum, $prepaymentAmount, 2) !== 0) {
            throw new BusinessException("发票总金额{$newInvoiceAmountSum}与预存金额" .
                "{$prepaymentAmount}不一致，请检查");
        }

        //获取供应商信息
        $providerMerchant = app(ProviderMerchantRepository::class)
            ->getProviderMerchantByProviderMerchantId($params['provider_merchant_id']);
        //获取签约主体名称
        $ekuaibaoCompanyRepository = new EkuaibaoCompanyRepository();
        $ekuaibaoCompanyName = $ekuaibaoCompanyRepository->getCompanyNameByCode($params['company_id']);
        //获取旧的相同发票
        $oldSameInvoices = $this->getOldSameInvoices($providerMerchantInvoiceDTOs);
        //校验单张发票信息
        $this->checkSingleInvoice($providerMerchantInvoiceDTOs, [
            'providerMerchantSubjectName' => $providerMerchant->merchantname ?? '',
            'ekuaibaoCompanyName' => $ekuaibaoCompanyName,
            'oldSameInvoices' => $oldSameInvoices,
        ]);
    }

    /**
     * @param ProviderMerchantInvoiceDTO[] $providerMerchantInvoiceDTOs
     * @param $prepaymentInfo
     * @return void
     * @throws BusinessException
     */
    private function checkSingleInvoice(array $providerMerchantInvoiceDTOs, $prepaymentInfo)
    {
        /**@var Collection|ProviderMerchantInvoice[] $oldSameInvoices */
        $oldSameInvoices = $prepaymentInfo['oldSameInvoices'];
        $newInvoiceNumberAndCodes = [];
        foreach ($providerMerchantInvoiceDTOs as $providerMerchantInvoiceDTO) {
            $sameInvoice = $oldSameInvoices
                ->where('invoice_number', $providerMerchantInvoiceDTO->getInvoiceNumber())
                ->when(!empty($providerMerchantInvoiceDTO->getInvoiceCode()),
                    function ($query) use ($providerMerchantInvoiceDTO) {
                        return $query->where('invoice_code', $providerMerchantInvoiceDTO->getInvoiceCode());
                    })->first();
            if (!empty($sameInvoice) && $sameInvoice->id != $providerMerchantInvoiceDTO->getInvoiceId()) {
                throw new BusinessException("发票{$providerMerchantInvoiceDTO->getInvoiceNumber()}已存在，" .
                    "请检查");
            }
            $providerMerchantSubjectName = StringHelper::normalizeBrackets($prepaymentInfo['providerMerchantSubjectName']);
            $invoiceSellerName = StringHelper::normalizeBrackets($providerMerchantInvoiceDTO->getSellerName());
            if ($invoiceSellerName != $providerMerchantSubjectName) {
                throw new BusinessException("开票主体{$invoiceSellerName}与供应商主体" .
                    "{$providerMerchantSubjectName}不一致，请检查");
            }
            $ekuaibaoCompanyName = $prepaymentInfo['ekuaibaoCompanyName'];
            if ($providerMerchantInvoiceDTO->getBuyerName() != $ekuaibaoCompanyName) {
                throw new BusinessException("发票抬头{$providerMerchantInvoiceDTO->getBuyerName()}与签约主体" .
                    "{$ekuaibaoCompanyName}不一致，请检查");
            }
            $currentYear = date('Y');
            if (date('Y', strtotime($providerMerchantInvoiceDTO->getInvoiceTime())) != $currentYear) {
                throw new BusinessException("发票开票时间{$providerMerchantInvoiceDTO->getInvoiceTime()}" .
                    "非当前年份{$currentYear}，请检查");
            }

            //本次上传的发票号码和代码是否重复
            $newInvoiceNumberAndCode = $providerMerchantInvoiceDTO->getInvoiceNumber();
            if (!empty($providerMerchantInvoiceDTO->getInvoiceCode())) {
                $newInvoiceNumberAndCode .= '-' . $providerMerchantInvoiceDTO->getInvoiceCode();
            }
            if (in_array($newInvoiceNumberAndCode, $newInvoiceNumberAndCodes)) {
                throw new BusinessException($newInvoiceNumberAndCode . '重复，请检查');
            }
            $newInvoiceNumberAndCodes[] = $newInvoiceNumberAndCode;
        }
    }

    /**
     * 保存供应商预存发票及其关联关系
     * @param $params
     * @return void
     * @throws \Exception
     */
    public function savePrepaymentInvoicesAndRelations($params)
    {
        $prepaymentIds = $params['prepayment_ids'];
        $invoices = $params['invoices'];
        if (empty($prepaymentIds)) {
            return;
        }
        $this->crmAccountId = $params['crm_account_id'];
        $this->checkCrmAccount();

        //保存预存发票及其关联关系前的校验
        $this->checkBeforeSavePrepaymentInvoicesAndRelations($params);

        DB::beginTransaction();
        //保存发票列表
        $providerMerchantInvoiceService = new ProviderMerchantInvoiceService();
        $newInvoiceDTOs = $providerMerchantInvoiceService->buildProviderMerchantInvoiceDTOs($invoices);
        $newInvoiceDTOs = $providerMerchantInvoiceService->saveInvoices($newInvoiceDTOs);

        //保存供应商发票和供应商预存申请的关联关系
        $this->addInvoiceToPrepaymentRelation($prepaymentIds, $newInvoiceDTOs);

        //更新预存申请的开票状态
        $providerMerchantPrepaymentService = new ProviderMerchantPrepaymentService();
        $providerMerchantPrepaymentService->savePrepaymentsInvoiceStatus($prepaymentIds);

        //创建发票核销单据申请
        $reconciliationId = $this->createInvoiceReconciliationIfPaid($prepaymentIds, $newInvoiceDTOs);

        DB::commit();

        if ($reconciliationId > 0) {
            //创建易快报核销单据
            $ekuaibaoPrepaymentReconciliationFlowProcessor = new EkuaibaoPrepaymentReconciliationFlowProcessor([
                'reconciliationApplicationId' => $reconciliationId,
                'crmAccountId' => $this->crmAccountId,
            ]);
            $ekuaibaoPrepaymentReconciliationFlowProcessor->process();
        }
    }


    /**
     * 校验当前操作人是否有效
     * @return void
     * @throws BusinessException
     */
    private function checkCrmAccount()
    {
        if (empty($this->crmAccountId)) {
            throw new BusinessException(MessageConstants::INVALID_OPERATOR);
        }
        $crmAccountRepository = new CrmAccountRepository();
        $crmAccount = $crmAccountRepository->getCrmAccountByCrmAccountId($this->crmAccountId);
        if (empty($crmAccount)) {
            throw new BusinessException(MessageConstants::INVALID_OPERATOR);
        }
    }

    /**
     * 保存供应商发票和供应商预存申请的关联关系
     *
     * @param array $newPrepaymentIds
     * @param ProviderMerchantInvoiceDTO[] $newInvoiceDTOs
     * @return void
     */
    private function addInvoiceToPrepaymentRelation(array $newPrepaymentIds, array $newInvoiceDTOs)
    {
        $providerMerchantPrepaymentService = new ProviderMerchantPrepaymentService();
        $prepaymentWithAmountsMap = $providerMerchantPrepaymentService->getPrepaymentWithAmountsMapByPrepaymentIds(
            $newPrepaymentIds);
        $providerMerchantInvoiceRepository = new ProviderMerchantInvoiceRepository();
        foreach ($newInvoiceDTOs as $newInvoiceDTO) {
            $unrelatedAmount = $newInvoiceDTO->getInvoiceAmount();
            foreach ($prepaymentWithAmountsMap as $prepaymentId => $prepayment) {
                $toBeRelateAmount = min($unrelatedAmount, $prepayment['unRelatedInvoiceAmount']);
                $unrelatedAmount = $unrelatedAmount - $toBeRelateAmount;
                $invoicePaymentOrder = $prepayment['status'] == ProviderMerchantPrepaymentConstants::STATUS_PAID ?
                    ProviderMerchantPrepaymentConstants::INVOICE_PAYMENT_ORDER_PAYMENT_FIRST :
                    ProviderMerchantPrepaymentConstants::INVOICE_PAYMENT_ORDER_INVOICE_FIRST;
                $insertData = [
                    'provider_merchant_invoice_id' => $newInvoiceDTO->getInvoiceId(),
                    'relation_type' => ProviderMerchantInvoiceReconciliationConstants::RECONCILE_TYPE_PREPAYMENT,
                    'related_application_id' => $prepaymentId,
                    'relate_amount' => $toBeRelateAmount,
                    'invoice_payment_order' => $invoicePaymentOrder,];
                $providerMerchantInvoiceRepository->saveInvoiceToPrepaymentRelation($insertData);
                $prepaymentWithAmountsMap[$prepaymentId]['unRelatedInvoiceAmount'] -= $toBeRelateAmount;
            }
        }
    }

    /**
     * 如果预存申请的开票付款顺序是先款后票并且已回款，那么创建发票核销单据申请
     *
     * @param array $prepaymentIds
     * @param ProviderMerchantInvoiceDTO[] $invoiceDTOs
     * @return int
     * @throws BusinessException
     */
    private function createInvoiceReconciliationIfPaid(array $prepaymentIds, array $invoiceDTOs): int
    {
        $reconciliationId = 0;
        $providerMerchantPrepaymentRepository = new ProviderMerchantPrepaymentRepository();
        $prepayments = $providerMerchantPrepaymentRepository->getPrepaymentsByPrepaymentIds([$prepaymentIds[0]]);
        /**
         * @var $firstPrepayment ProviderPrestore
         */
        $firstPrepayment = $prepayments->first();
        if ($firstPrepayment->invoice_payment_order ==
            ProviderMerchantPrepaymentConstants::INVOICE_PAYMENT_ORDER_PAYMENT_FIRST &&
            $firstPrepayment->status == ProviderMerchantPrepaymentConstants::STATUS_PAID) {
            $reconciliationId = app(ProviderMerchantInvoiceReconciliationService::class)
                ->createInvoiceReconciliationForPrepaymentAndSaveRelations($prepaymentIds, $invoiceDTOs,
                    $this->crmAccountId);
        }
        return $reconciliationId;
    }
}
