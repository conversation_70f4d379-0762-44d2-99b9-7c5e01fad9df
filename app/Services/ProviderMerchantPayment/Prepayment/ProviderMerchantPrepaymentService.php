<?php

namespace App\Services\ProviderMerchantPayment\Prepayment;

use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants as ReconciliationConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantPaymentErrorLogConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantPrepaymentConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantPrepaymentConstants as PrepaymentConstants;
use App\Constants\ProviderMerchantPayment\SettleInfoConstants;
use App\Entities\ProviderMerchantPayment\PrepaymentContextMapEntity;
use App\Entities\ProviderMerchantPayment\ProviderMerchantDTO;
use App\Entities\ProviderMerchantPayment\ProviderMerchantPrepaymentDTO;
use App\Exceptions\BusinessException;
use App\Models\ProviderMerchantPaymentErrorLog;
use App\Models\ProviderMerchantSettleInfo;
use App\Models\ProviderPrestore;
use App\Repositories\Admin\AdminRepository;
use App\Repositories\CrmAccount\CrmAccountRepository;
use App\Repositories\Ekuaibao\EkuaibaoCompanyRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPaymentErrorLogRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPrepaymentLogRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPrepaymentRepository as PrepaymentRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantSettleInfoRepository;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFlowService;
use App\Services\ProviderMerchantPayment\EkuaibaoFlow\PrepaymentEkuaibaoCorporatePaymentFlowProcessor;
use App\Services\ProviderMerchantPayment\EkuaibaoFlow\PrepaymentEkuaibaoPrepaymentFlowProcessor;
use App\Services\ProviderMerchantPayment\ProviderMerchantPaymentService;
use Illuminate\Database\Eloquent\Collection;
use Yanqu\YanquPhplib\Curl\CurlUtil;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\YqLog\YqLog;

class ProviderMerchantPrepaymentService
{
    private $crmAccountId;

    public function getProviderMerchantPaymentErrorLogs($params): array
    {
        $providerMerchantPaymentErrorLogRepository = new ProviderMerchantPaymentErrorLogRepository();
        $builder = $providerMerchantPaymentErrorLogRepository->getPrepaymentErrorLogBuilder($params);
        $count = $builder->count();
        $pageSize = $params['page_size'] ?? 10;
        $providerMerchantPaymentErrorLogs = $builder
            ->select('id', 'step', 'error_content', 'crm_account_id', 'admin_account_id', 'create_time')
            ->paginate($pageSize)->items();

        $crmAccountIds = collect($providerMerchantPaymentErrorLogs)->pluck('crm_account_id')->unique()->toArray();
        $crmAccountRepository = new CrmAccountRepository();
        $crmAccountNameMap = $crmAccountRepository->getCrmAccountNameMapByCrmAccountIds($crmAccountIds)->toArray();
        $adminAccountIds = collect($providerMerchantPaymentErrorLogs)->pluck('admin_account_id')->unique()->toArray();
        $adminAccountRepository = new AdminRepository();
        $adminAccounts = $adminAccountRepository->getAdminAccountsByAdminIds($adminAccountIds);
        $adminAccountNameMap = $adminAccounts->pluck('realname', 'adminid')->toArray();
        $returnList = [];
        foreach ($providerMerchantPaymentErrorLogs as $providerMerchantPaymentErrorLog) {
            $returnList[] = $this->buildProviderMerchantPaymentErrorLog($providerMerchantPaymentErrorLog,
                $crmAccountNameMap, $adminAccountNameMap);
        }
        return [
            'list' => $returnList,
            'total' => $count,
        ];
    }

    /**
     * @param ProviderMerchantPaymentErrorLog $providerMerchantPaymentErrorLog
     * @param array $crmAccountNameMap
     * @param array $adminAccountNameMap
     * @return array
     */
    private function buildProviderMerchantPaymentErrorLog(
        ProviderMerchantPaymentErrorLog $providerMerchantPaymentErrorLog, array $crmAccountNameMap,
        array                           $adminAccountNameMap): array
    {
        $stepNameMap = ProviderMerchantPaymentErrorLogConstants::STEP_NAME_MAP;
        $crmAccountName = $crmAccountNameMap[$providerMerchantPaymentErrorLog->crm_account_id] ?? '';
        $adminAccountName = $adminAccountNameMap[$providerMerchantPaymentErrorLog->admin_account_id] ?? '';
        return [
            'create_time' => $providerMerchantPaymentErrorLog->create_time,
            'operate_account_name' => $crmAccountName ?: $adminAccountName,
            'step_name' => $stepNameMap[$providerMerchantPaymentErrorLog->step] ?? '',
            'error_content' => $providerMerchantPaymentErrorLog->error_content,
        ];
    }

    /**
     * 保存预存申请的开票状态
     *
     * @param array $prepaymentIds
     * @return void
     */
    public function savePrepaymentsInvoiceStatus(array $prepaymentIds)
    {
        $prepaymentAmountsMap = $this->getPrepaymentWithAmountsMapByPrepaymentIds($prepaymentIds);
        $prepaymentRepository = new PrepaymentRepository();
        foreach ($prepaymentIds as $prepaymentId) {
            $prepayment = $prepaymentAmountsMap[$prepaymentId] ?? [];
            $prepaymentAmount = $prepayment['prepaymentAmount'];
            $relatedInvoiceAmount = $prepayment['relatedInvoiceAmount'];

            if (bccomp($prepaymentAmount, $relatedInvoiceAmount, 2) == 0) {
                $invoiceStatus = PrepaymentConstants::INVOICE_STATUS_INVOICED;
            } elseif (bccomp($prepaymentAmount, $relatedInvoiceAmount, 2) == 1 && $relatedInvoiceAmount > 0) {
                $invoiceStatus = PrepaymentConstants::INVOICE_STATUS_PARTIAL_INVOICED;
            } else {
                $invoiceStatus = PrepaymentConstants::INVOICE_STATUS_NOT_INVOICED;
            }
            $prepaymentRepository->updateInvoiceStatus($prepaymentId, $invoiceStatus);
        }
    }

    /**
     * 获取预存申请的金额映射
     *
     * @param array $prepaymentIds
     * @return List<int,array{
     *     status: int,
     *     prepaymentAmount: numeric,
     *     relatedInvoiceAmount: numeric,
     *     unRelatedInvoiceAmount: numeric}>
     */
    public function getPrepaymentWithAmountsMapByPrepaymentIds(array $prepaymentIds): array
    {
        //获取预存申请列表
        $prepaymentRepository = new PrepaymentRepository();
        $prepayments = $prepaymentRepository->getPrepaymentsByPrepaymentIds($prepaymentIds);

        //获取已关联发票金额映射
        $providerMerchantInvoiceRelateAmountMap = $prepaymentRepository
            ->getProviderMerchantInvoiceRelateAmountMapByPrepaymentIds($prepaymentIds);

        $returnMap = [];
        //获取映射
        foreach ($prepaymentIds as $prepaymentId) {
            $prepayment = $prepayments->where('prestoreid', $prepaymentId)->first();
            $returnMap[$prepaymentId] = $prepayment->toArray();
            $prepaymentAmount = $prepayment->amount;
            $returnMap[$prepaymentId]['prepaymentAmount'] = $prepaymentAmount;
            $relatedInvoiceAmount = $providerMerchantInvoiceRelateAmountMap[$prepaymentId] ?? 0;
            $returnMap[$prepaymentId]['relatedInvoiceAmount'] = $relatedInvoiceAmount;
            $returnMap[$prepaymentId]['unRelatedInvoiceAmount'] = $prepaymentAmount - $relatedInvoiceAmount;
        }
        return $returnMap;
    }

    public function getPrepayments($params): array
    {
        $prepaymentRepository = new PrepaymentRepository();
        $builder = $prepaymentRepository->getPrepaymentsBuilder($params);
        $count = $builder->count();

        $prepayments = $builder->select('provider_prestore.prestoreid', 'provider_prestore.providermerchantid',
            'provider_prestore.prestore_type', 'provider_prestore.amount', 'provider_prestore.settleinfoid',
            'settleinfojson', 'provider_prestore.paymentmode', 'provider_prestore.remark',
            'provider_prestore.applytime',
            'provider_prestore.applyaccount', 'provider_prestore.status', 'provider_prestore.invoice_status',
            'provider_prestore.paytime', 'provider_prestore.adminid',
            'provider_prestore.company_id', 'provider_merchant.merchantname', 'provider_merchant.contacter',
            'provider_merchant.saccountid as provider_merchant_responsible_crm_account_id',
            'provider_prestore.ekuaibao_flow_operator_crm_account_id', 'provider_prestore.ekuaibao_flow_code')
            ->orderBy('provider_prestore.applytime', 'desc')
            ->paginate($params['page_size'] ?? 20)->items();
        $returnPrepayments = [];
        $prepaymentContextMapEntity = $this->buildPrepaymentContextMapEntity($prepayments);
        foreach ($prepayments as $prepayment) {
            $providerMerchantPrepaymentDTO = $this->buildProviderMerchantPaymentDTO($prepayment);
            $returnPrepayments[] = $this->buildPrepayment($providerMerchantPrepaymentDTO, $prepaymentContextMapEntity);
        }
        return [
            'total' => $count,
            'list' => $returnPrepayments,
            'page' => $params['page'] ?? 1,
        ];
    }

    private function buildPrepaymentContextMapEntity($prepayments): PrepaymentContextMapEntity
    {
        $prepaymentContextMapEntity = new PrepaymentContextMapEntity();
        $ekuaibaoCompanyRepository = new EkuaibaoCompanyRepository();
        //易快报公司名称映射
        $ekuaibaoCompanyNameMap = $ekuaibaoCompanyRepository->getCompanyNameMap();
        $prepaymentContextMapEntity->setEkuaibaoCompanyNameMap($ekuaibaoCompanyNameMap);

        //crm账号名称映射
        $applyAccountIds = collect($prepayments)->pluck('applyaccount')->unique()->toArray();
        $providerMerchantResponsibleCrmAccountIds = collect($prepayments)
            ->pluck('provider_merchant_responsible_crm_account_id')->unique()->toArray();
        $crmAccountIds = array_merge($applyAccountIds, $providerMerchantResponsibleCrmAccountIds);
        $crmAccountNameMap = (new CrmAccountRepository())->getCrmAccountNameMapByCrmAccountIds($crmAccountIds);
        $prepaymentContextMapEntity->setCrmAccountNameMap($crmAccountNameMap);

        //供应商预存余额映射
        $providerMerchantPrepaymentLogRepository = new ProviderMerchantPrepaymentLogRepository();
        $prepaymentBalanceMap = $providerMerchantPrepaymentLogRepository->getPrepaymentBalanceMapByProviderMerchantIds(
            collect($prepayments)->pluck('providermerchantid')->unique()->toArray());
        $prepaymentContextMapEntity->setPrepaymentBalanceMap($prepaymentBalanceMap);

        //获取已关联发票金额映射
        $prepaymentIds = collect($prepayments)->pluck('prestoreid')->toArray();
        $prepaymentRepository = new PrepaymentRepository();
        $invoiceRelateAmountMap = $prepaymentRepository
            ->getProviderMerchantInvoiceRelateAmountMapByPrepaymentIds($prepaymentIds);
        $prepaymentContextMapEntity->setInvoiceRelateAmountMap($invoiceRelateAmountMap);

        //核销映射
        $prepaymentRepository = new PrepaymentRepository();
        $reconciles = $prepaymentRepository->getReconcileRelationsByPrepaymentIds($prepaymentIds);
        $reconcilesMap = $reconciles->groupBy('prepayment_id');
        $prepaymentContextMapEntity->setReconcilesMap($reconcilesMap);

        //结算信息映射
        $providerMerchantIds = collect($prepayments)->pluck('providermerchantid')->filter()->unique()->toArray();
        $providerMerchantSettleInfoRepository = new ProviderMerchantSettleInfoRepository();
        $settleInfos = $providerMerchantSettleInfoRepository->getSettleInfosByProviderMerchantIds($providerMerchantIds);
        $settleInfosMap = $settleInfos->groupBy('providermerchantid');
        $prepaymentContextMapEntity->setSettleInfosMap($settleInfosMap);

        //列表所有的最后一次失败日志映射
        $providerMerchantPaymentErrorLogRepository = new ProviderMerchantPaymentErrorLogRepository();
        $lastReconcileErrorLogMap = $providerMerchantPaymentErrorLogRepository
            ->getLastReconcileErrorLogMapByPrepaymentIds($prepaymentIds);
        $prepaymentContextMapEntity->setLastReconcileErrorLogMap($lastReconcileErrorLogMap);
        return $prepaymentContextMapEntity;
    }

    private function buildProviderMerchantPaymentDTO(ProviderPrestore $prepayment): ProviderMerchantPrepaymentDTO
    {
        $providerMerchantPrepaymentDTO = new ProviderMerchantPrepaymentDTO($prepayment->toArray());
        $providerMerchantPrepaymentDTO->setPrepaymentId($prepayment->prestoreid);
        $providerMerchantPrepaymentDTO->setProviderMerchantId($prepayment->providermerchantid);
        $providerMerchantPrepaymentDTO->setPrepaymentType($prepayment->prestore_type);
        $providerMerchantPrepaymentDTO->setBoundOrderIds($prepayment->bound_orderids);
        $providerMerchantPrepaymentDTO->setDeductedOrderIds($prepayment->deducted_orderids);
        $providerMerchantPrepaymentDTO->setSettleInfoId($prepayment->settleinfoid);
        $providerMerchantPrepaymentDTO->setSettleInfoJson($prepayment->settleinfojson);
        $providerMerchantPrepaymentDTO->setOrderNos($prepayment->osns);
        $providerMerchantPrepaymentDTO->setApplyTime($prepayment->applytime);
        $providerMerchantPrepaymentDTO->setApplyCrmAccountAccountId($prepayment->applyaccount);
        $providerMerchantPrepaymentDTO->setPayTime($prepayment->paytime);
        $providerMerchantPrepaymentDTO->setDelAccount($prepayment->delaccount);
        $providerMerchantPrepaymentDTO->setAdminId($prepayment->adminid);

        $providerMerchantDTO = new ProviderMerchantDTO([]);
        $providerMerchantDTO->setProviderMerchantId($prepayment->providermerchantid);
        $providerMerchantDTO->setContactName($prepayment->contacter ?? '');
        $providerMerchantDTO->setMerchantName($prepayment->merchantname ?? '');
        $providerMerchantDTO->setIdCardName($prepayment->idcardname ?? '');
        $providerMerchantDTO->setCharacter($prepayment->character ?? '');
        $providerMerchantDTO->setResponsibleCrmAccountId(
            $prepayment->provider_merchant_responsible_crm_account_id ?? 0);
        $providerMerchantDTO->setLoginName($prepayment->loginame ?? '');

        $providerMerchantPrepaymentDTO->setProviderMerchantDTO($providerMerchantDTO);
        return $providerMerchantPrepaymentDTO;
    }

    private function buildPrepayment(ProviderMerchantPrepaymentDTO $providerMerchantPrepaymentDTO,
                                     PrepaymentContextMapEntity    $prepaymentContextMapEntity): array
    {
        $paymentModeNameMap = PrepaymentConstants::PAYMENT_MODE_NAME_MAP;
        $statusNameMap = PrepaymentConstants::STATUS_NAME_MAP;
        $prepaymentTypeNameMap = PrepaymentConstants::PREPAYMENT_TYPE_NAME_MAP;
        $invoiceStatusNameMap = PrepaymentConstants::INVOICE_STATUS_NAME_MAP;
        $ekuaibaoCompanyNameMap = $prepaymentContextMapEntity->getEkuaibaoCompanyNameMap();
        $crmAccountNameMap = $prepaymentContextMapEntity->getCrmAccountNameMap();
        $prepaymentBalanceMap = $prepaymentContextMapEntity->getPrepaymentBalanceMap();
        $storedSettleInfo = json_decode($providerMerchantPrepaymentDTO->getSettleInfoJson(), true);
        $companyId = $providerMerchantPrepaymentDTO->getCompanyId() ?? '';
        $companyId = empty($companyId) && !empty($storedSettleInfo['company_id']) ? $storedSettleInfo['company_id'] :
            $companyId;
        $lastReconcileErrorLogMap = $prepaymentContextMapEntity->getLastReconcileErrorLogMap();
        $providerMerchantDTO = $providerMerchantPrepaymentDTO->getProviderMerchantDTO();
        $prepaymentId = $providerMerchantPrepaymentDTO->getPrepaymentId();
        $reconciliations = $this->getReconciliationsForPrepaymentList($prepaymentContextMapEntity, $prepaymentId);
        $invoiceRelateAmountMap = $prepaymentContextMapEntity->getInvoiceRelateAmountMap();
        $invoiceRelateAmount = $invoiceRelateAmountMap[$prepaymentId] ?? "0.00";
        $canSaveEkuaibaoFlowCode = false;
        if($providerMerchantPrepaymentDTO->getStatus() == PrepaymentConstants::STATUS_APPLIED &&
            $providerMerchantPrepaymentDTO->getEkuaibaoFlowOperatorCrmAccountId() != env("CRM_ACCOUNT_ID_SYSTEM")
        ) {
            $canSaveEkuaibaoFlowCode = true;
        }
        $paymentMode = $storedSettleInfo['paymentmode'] ?? '';
        return [
            'prepayment_id' => $prepaymentId,
            'formated_apply_time' => date('Y-m-d', $providerMerchantPrepaymentDTO->getApplyTime()),
            'provider_merchant_contact_name' => $providerMerchantDTO->getContactName(),
            'provider_merchant_name' => $providerMerchantDTO->getMerchantName(),
            'prepayment_type_name' =>
                $prepaymentTypeNameMap[$providerMerchantPrepaymentDTO->getPrepaymentType()] ?? '-',
            'amount' => $providerMerchantPrepaymentDTO->getAmount(),
            'prepayment_balance' => $prepaymentBalanceMap[$providerMerchantPrepaymentDTO->getProviderMerchantId()] ??
                "0.00",
            'ekuaibao_company_name' => $ekuaibaoCompanyNameMap[$companyId] ?? '',
            'apply_crm_account_name' =>
                $crmAccountNameMap[$providerMerchantPrepaymentDTO->getApplyCrmAccountAccountId()] ?? '',
            'responsible_crm_account_name' => $crmAccountNameMap[$providerMerchantDTO->getResponsibleCrmAccountId()] ??
                '',
            'payment_mode_name' => $paymentModeNameMap[$paymentMode] ?? '',
            'status' => $providerMerchantPrepaymentDTO->getStatus(),
            'status_name' => $statusNameMap[$providerMerchantPrepaymentDTO->getStatus()] ?? '',
            'invoice_status_name' => $invoiceStatusNameMap[$providerMerchantPrepaymentDTO->getInvoiceStatus()] ?? '',
            'unrelated_invoice_amount' => number_format(round($providerMerchantPrepaymentDTO->getAmount()
                - $invoiceRelateAmount, 2), 2, '.', ''),
            'ekuaibao_flow_code' => !empty($providerMerchantPrepaymentDTO->getEkuaibaoFlowCode()) ?
                $providerMerchantPrepaymentDTO->getEkuaibaoFlowCode() : '--',
            'reconciliations' => $reconciliations,
            'remark' => $providerMerchantPrepaymentDTO->getRemark(),
            'has_error_log' => isset($lastReconcileErrorLogMap[$prepaymentId]),
            'can_save_ekuaibao_flow_code' => $canSaveEkuaibaoFlowCode,
        ];
    }

    private function getReconciliationsForPrepaymentList($prepaymentContextMapEntity, $prepaymentId): array
    {
        $reconcilesMap = $prepaymentContextMapEntity->getReconcilesMap();
        /** @var $reconciles Collection */
        $reconciles = $reconcilesMap[$prepaymentId] ?? [];
        $returnReconciles = [];
        foreach ($reconciles as $reconcile) {
            $returnReconciles[] = [
                'reconciliation_application_id' => $reconcile->reconciliation_application_id,
                'ekuaibao_flow_code' =>  !empty($reconcile->ekuaibao_flow_code) ? $reconcile->ekuaibao_flow_code : '--',
                'process_status_name' => ReconciliationConstants::PROCESS_STATUS_MAP[$reconcile->process_status] ?? '',
            ];
        }
        return $returnReconciles;
    }

    public function getExportPrepayments($params): array
    {
        $prepaymentRepository = new PrepaymentRepository();
        $builder = $prepaymentRepository->getPrepaymentsBuilder($params);
        $prepayments = $builder->select('provider_prestore.prestoreid', 'provider_prestore.providermerchantid',
            'provider_prestore.prestore_type', 'provider_prestore.amount', 'provider_prestore.settleinfojson',
            'provider_prestore.paymentmode', 'provider_prestore.remark',
            'provider_prestore.applytime', 'provider_prestore.status', 'provider_prestore.paytime',
            'provider_prestore.company_id', 'provider_merchant.merchantname', 'provider_merchant.contacter',
            'provider_prestore.applyaccount', 'provider_prestore.invoice_status',
            'provider_prestore.ekuaibao_flow_code')
            ->orderBy('provider_prestore.applytime', 'desc')
            ->get();
        $returnPrepayments = [];
        $prepaymentContextMapEntity = $this->buildPrepaymentContextMapEntity($prepayments);
        foreach ($prepayments as $prepayment) {
            $providerMerchantPrepaymentDTO = $this->buildProviderMerchantPaymentDTO($prepayment);
            $returnPrepayments[] = $this->buildExportPrepayment($providerMerchantPrepaymentDTO,
                $prepaymentContextMapEntity);
        }
        return [
            'name' => PrepaymentConstants::PREPAYMENT_EXPORT_NAME_MAP,
            'list' => $returnPrepayments,
        ];
    }

    /**
     * @param ProviderMerchantPrepaymentDTO $providerMerchantPrepaymentDTO
     * @param PrepaymentContextMapEntity $prepaymentContextMapEntity
     * @return array
     */
    private function buildExportPrepayment(ProviderMerchantPrepaymentDTO $providerMerchantPrepaymentDTO,
                                           PrepaymentContextMapEntity    $prepaymentContextMapEntity): array
    {
        $paymentModeNameMap = PrepaymentConstants::PAYMENT_MODE_NAME_MAP;
        $statusNameMap = PrepaymentConstants::STATUS_NAME_MAP;
        $prepaymentTypeNameMap = PrepaymentConstants::PREPAYMENT_TYPE_NAME_MAP;
        $invoiceStatusNameMap = PrepaymentConstants::INVOICE_STATUS_NAME_MAP;
        $ekuaibaoCompanyNameMap = $prepaymentContextMapEntity->getEkuaibaoCompanyNameMap();
        $crmAccountNameMap = $prepaymentContextMapEntity->getCrmAccountNameMap();
        $providerMerchantId = $providerMerchantPrepaymentDTO->getProviderMerchantId();
        /** @var $settleInfosMap Collection */
        $settleInfosMap = $prepaymentContextMapEntity->getSettleInfosMap();
        /** @var $settleInfo ProviderMerchantSettleInfo */
        $settleInfo = isset($settleInfosMap[$providerMerchantId]) ? $settleInfosMap[$providerMerchantId]->first() : null;
        $storedSettleInfo = json_decode($providerMerchantPrepaymentDTO->getSettleInfoJson(), true);
        $companyId = $providerMerchantPrepaymentDTO->getCompanyId() ?? '';
        $companyId = empty($companyId) && !empty($storedSettleInfo['company_id']) ? $storedSettleInfo['company_id'] :
            $companyId;
        $providerMerchantDTO = $providerMerchantPrepaymentDTO->getProviderMerchantDTO();
        $prepaymentId = $providerMerchantPrepaymentDTO->getPrepaymentId();
        $reconciliations = $this->getReconciliationsForPrepaymentList($prepaymentContextMapEntity, $prepaymentId);
        $exportReconciliations = array_map(function ($reconciliation) {
            unset($reconciliation['reconciliation_application_id']);
            return implode(' ', $reconciliation);
        }, $reconciliations);
        $exportReconciliations = implode(';', $exportReconciliations);
        $invoiceRelateAmountMap = $prepaymentContextMapEntity->getInvoiceRelateAmountMap();
        $invoiceRelateAmount = $invoiceRelateAmountMap[$prepaymentId] ?? "0.00";
        $paymentMode = $storedSettleInfo['paymentmode'] ?? '';
        return [
            'prepayment_id' => $prepaymentId,
            'provider_merchant_id' => $providerMerchantId,
            'provider_merchant_contact_name' => $providerMerchantDTO->getContactName(),
            'provider_merchant_name' => $providerMerchantDTO->getMerchantName(),
            'bank_name' => !empty($settleInfo->bankname) ? (string)$settleInfo->bankname : '',
            'payment_mode_name' => $paymentModeNameMap[$paymentMode] ?? '',
            'amount' => $providerMerchantPrepaymentDTO->getAmount(),
            'remark' => $providerMerchantPrepaymentDTO->getRemark(),
            'ekuaibao_company_name' => $ekuaibaoCompanyNameMap[$companyId] ?? '',
            'apply_crm_account_name' =>
                $crmAccountNameMap[$providerMerchantPrepaymentDTO->getApplyCrmAccountAccountId()] ?? '',
            'formated_apply_time' => date('Y-m-d', $providerMerchantPrepaymentDTO->getApplyTime()),
            'status_name' => $statusNameMap[$providerMerchantPrepaymentDTO->getStatus()] ?? '',
            'formated_pay_time' => !empty($providerMerchantPrepaymentDTO->getPayTime()) ?
                date('Y-m-d H:i', $providerMerchantPrepaymentDTO->getPayTime()) : '--',
            'prepayment_type_name' =>
                $prepaymentTypeNameMap[$providerMerchantPrepaymentDTO->getPrepaymentType()] ?? '',
            'invoice_status_name' => $invoiceStatusNameMap[$providerMerchantPrepaymentDTO->getInvoiceStatus()] ?? '',
            'unrelated_invoice_amount' => number_format(round($providerMerchantPrepaymentDTO->getAmount()
                - $invoiceRelateAmount, 2), 2, '.', ''),
            'ekuaibao_flow_code' => !empty($providerMerchantPrepaymentDTO->getEkuaibaoFlowCode()) ?
                $providerMerchantPrepaymentDTO->getEkuaibaoFlowCode() : '--',
            'reconciliations' => $exportReconciliations,
        ];
    }

    public function getPrepaymentDetail($params)
    {
        $prepaymentRepository = new PrepaymentRepository();
        $builder = $prepaymentRepository->getPrepaymentsBuilder($params);
        $prepayments = $builder->select('provider_prestore.prestoreid', 'provider_prestore.providermerchantid',
            'provider_prestore.prestore_type', 'provider_prestore.amount', 'provider_prestore.settleinfoid',
            'settleinfojson', 'provider_prestore.paymentmode', 'provider_prestore.remark',
            'provider_prestore.applytime', 'provider_prestore.applyaccount', 'provider_prestore.status',
            'provider_prestore.invoice_status', 'provider_prestore.company_id', 'provider_prestore.ekuaibao_flow_code',
            'provider_merchant.merchantname', 'provider_merchant.contacter', 'provider_merchant.loginame',
        'provider_prestore.invoice_payment_order')
            ->limit(1)->get();
        if($prepayments->count() == 0) {
            throw new BusinessException(MessageConstants::PREPAYMENT_NOT_EXIST);
        }
        $prepaymentContextMapEntity = $this->buildPrepaymentContextMapEntity($prepayments);
        $providerMerchantPrepaymentDTO = $this->buildProviderMerchantPaymentDTO($prepayments->first());
        $returnPrepayment = $this->buildPrepaymentDetail($providerMerchantPrepaymentDTO, $prepaymentContextMapEntity);
        $getPrepaymentAndDepositRemainingAmount = $this->getPrepaymentAndDepositRemainingAmount(
            $providerMerchantPrepaymentDTO->getProviderMerchantId());
        return array_merge($returnPrepayment, $getPrepaymentAndDepositRemainingAmount);
    }

    private function buildPrepaymentDetail(ProviderMerchantPrepaymentDTO $providerMerchantPrepaymentDTO,
                                           PrepaymentContextMapEntity    $prepaymentContextMapEntity): array
    {
        $statusNameMap = PrepaymentConstants::STATUS_NAME_MAP;
        $prepaymentTypeNameMap = PrepaymentConstants::PREPAYMENT_TYPE_NAME_MAP;
        $ekuaibaoCompanyNameMap = $prepaymentContextMapEntity->getEkuaibaoCompanyNameMap();
        $crmAccountNameMap = $prepaymentContextMapEntity->getCrmAccountNameMap();
        $storedSettleInfo = json_decode($providerMerchantPrepaymentDTO->getSettleInfoJson(), true);
        $companyId = $providerMerchantPrepaymentDTO->getCompanyId() ?? '';
        $companyId = empty($companyId) && !empty($storedSettleInfo['company_id']) ? $storedSettleInfo['company_id'] :
            $companyId;
        $providerMerchantDTO = $providerMerchantPrepaymentDTO->getProviderMerchantDTO();
        $prepaymentId = $providerMerchantPrepaymentDTO->getPrepaymentId();
        $reconciliations = $this->getReconciliationsForPrepaymentList($prepaymentContextMapEntity, $prepaymentId);
        $settleInfoText = $this->getPrepaymentDetailSettleInfoText($storedSettleInfo);
        return [
            'prepayment_id' => $prepaymentId,
            'formated_apply_time' => date('Y-m-d', $providerMerchantPrepaymentDTO->getApplyTime()),
            'apply_crm_account_name' =>
                $crmAccountNameMap[$providerMerchantPrepaymentDTO->getApplyCrmAccountAccountId()] ?? '',
            'provider_merchant_name' => $providerMerchantDTO->getMerchantName(),
            'provider_merchant_login_name' => $providerMerchantDTO->getLoginName(),
            'amount' => $providerMerchantPrepaymentDTO->getAmount(),
            'prepayment_type_name' =>
                $prepaymentTypeNameMap[$providerMerchantPrepaymentDTO->getPrepaymentType()] ?? '-',
            'provider_merchant_contact_name' => $providerMerchantDTO->getContactName(),
            'ekuaibao_company_name' => $ekuaibaoCompanyNameMap[$companyId] ?? '',
            'status_name' => $statusNameMap[$providerMerchantPrepaymentDTO->getStatus()] ?? '',
            'ekuaibao_flow_code' => !empty($providerMerchantPrepaymentDTO->getEkuaibaoFlowCode()) ?
                $providerMerchantPrepaymentDTO->getEkuaibaoFlowCode() : '--',
            'settle_info_text' => $settleInfoText,
            'remark' => $providerMerchantPrepaymentDTO->getRemark(),
            'invoice_status' => $providerMerchantPrepaymentDTO->getInvoiceStatus(),
            'reconciliations' => $reconciliations,
            'invoice_payment_order' => $providerMerchantPrepaymentDTO->getInvoicePaymentOrder(),
            'payment_mode' => $storedSettleInfo['paymentmode'] ?? '',
        ];
    }

    private function getPrepaymentDetailSettleInfoText($settleInfo): string
    {
        $paymentMode = $settleInfo['paymentmode'] ?? '';
        $settleInfoText = SettleInfoConstants::PAYMENT_MODE_NAME_MAP[$paymentMode] ?
            "(" . SettleInfoConstants::PAYMENT_MODE_NAME_MAP[$paymentMode] . ")" : '';
        switch ($settleInfo['paymentmethod']) {
            case SettleInfoConstants::PAYMENT_METHOD_ALIPAY:
                $settleInfoText .= '支付宝,账号:' . $settleInfo['alipayaccount'] . ',账号姓名:' . $settleInfo['alipayname'];
                break;
            case SettleInfoConstants::PAYMENT_METHOD_WECHAT:
                $settleInfoText .= '微信,账号:' . $settleInfo['wechatpay'];
                break;
            case SettleInfoConstants::PAYMENT_METHOD_BANK_CARD:
                $settleInfoText .= '银行卡,开户银行:' . $settleInfo['openingbank'] . ',户主:' . $settleInfo['bankname'] .
                    ',银行账号:' . $settleInfo['bankaccount'];
                break;
            default:
                break;
        }
        return $settleInfoText;
    }

    private function getPrepaymentAndDepositRemainingAmount($providerMerchantId): array
    {
        $prepaymentRepository = new PrepaymentRepository();
        $prepayments = $prepaymentRepository->getPaidPrepaymentsByProviderMerchantId($providerMerchantId);
        $prepaymentAmount = $prepayments
            ->where('prestore_type', ProviderMerchantPrepaymentConstants::PREPAYMENT_TYPE_PREPAY)
            ->sum('remaining_amount');
        $depositAmount = $prepayments
            ->where('prestore_type', ProviderMerchantPrepaymentConstants::PREPAYMENT_TYPE_DEPOSIT)
            ->sum('remaining_amount');
        return [
            'prepayment_remaining_amount' => $prepaymentAmount,
            'deposit_remaining_amount' => $depositAmount,
        ];
    }

    public function getPrepaymentsForUploadInvoice($params)
    {
        $prepaymentRepository = new PrepaymentRepository();
        $params['status'] = PrepaymentConstants::STATUS_PAID;
        $params['invoice_statuses'] = [PrepaymentConstants::INVOICE_STATUS_NOT_INVOICED,
            PrepaymentConstants::INVOICE_STATUS_PARTIAL_INVOICED];
        $params['payment_mode'] = ProviderMerchantPrepaymentConstants::PAYMENT_MODE_CORPORATE;
        $builder = $prepaymentRepository->getPrepaymentsBuilder($params);
        $count = $builder->count();

        $prepayments = $builder->select('provider_prestore.prestoreid', 'provider_prestore.providermerchantid',
            'provider_prestore.amount', 'provider_prestore.settleinfoid', 'provider_prestore.applytime',
            'provider_prestore.company_id', 'provider_merchant.merchantname', 'provider_merchant.contacter')
            ->orderBy('provider_prestore.applytime', 'desc')
            ->paginate($params['page_size'] ?? 20)->items();
        $returnPrepayments = [];

        $ekuaibaoCompanyRepository = new EkuaibaoCompanyRepository();
        //获取易快报公司名称映射
        $ekuaibaoCompanyNameMap = $ekuaibaoCompanyRepository->getCompanyNameMap();
        //获取已关联发票金额映射
        $prepaymentIds = collect($prepayments)->pluck('prestoreid')->toArray();
        $prepaymentRepository = new PrepaymentRepository();
        $invoiceRelateAmountMap = $prepaymentRepository
            ->getProviderMerchantInvoiceRelateAmountMapByPrepaymentIds($prepaymentIds);
        /**
         * @var ProviderPrestore $prepayment
         */
        foreach ($prepayments as $prepayment) {
            $providerMerchantPrepaymentDTO = $this->buildProviderMerchantPaymentDTO($prepayment);
            $providerMerchantDTO = $providerMerchantPrepaymentDTO->getProviderMerchantDTO();
            $prepaymentId = $providerMerchantPrepaymentDTO->getPrepaymentId();
            $storedSettleInfo = json_decode($providerMerchantPrepaymentDTO->getSettleInfoJson(), true);
            $companyId = $providerMerchantPrepaymentDTO->getCompanyId() ?? '';
            $companyId = empty($companyId) && !empty($storedSettleInfo['company_id']) ?
                $storedSettleInfo['company_id'] : $companyId;
            $invoiceRelateAmount = $invoiceRelateAmountMap[$prepaymentId] ?? "0.00";
            $returnPrepayment = [
                'prepayment_id' => $providerMerchantPrepaymentDTO->getPrepaymentId(),
                'provider_merchant_contact_name' => $providerMerchantDTO->getContactName(),
                'provider_merchant_name' => $providerMerchantDTO->getMerchantName(),
                'amount' => $providerMerchantPrepaymentDTO->getAmount(),
                'unrelated_invoice_amount' =>  number_format(round($providerMerchantPrepaymentDTO->getAmount() -
                    $invoiceRelateAmount, 2), 2, '.',''),
                'ekuaibao_company_name' => $ekuaibaoCompanyNameMap[$companyId] ?? '',
                'formated_apply_time' => date('Y-m-d', $providerMerchantPrepaymentDTO->getApplyTime()),
            ];
            $returnPrepayments[] = $returnPrepayment;
        }
        return [
            'total' => $count,
            'list' => $returnPrepayments,
        ];
    }

    /**
     * 保存预存申请的易快报单号
     * @param $params
     * @return void
     * @throws BusinessException|CurlException
     */
    public function saveEkuaibaoFlowCode($params)
    {
        $ekuaibaoFlowCode = $params['ekuaibao_flow_code'];
        $prepaymentId = $params['prepayment_id'];
        $this->crmAccountId = $params['crm_account_id'];
        $this->checkCrmAccount();

        //校验预存申请信息
        $prepaymentRepository = new PrepaymentRepository();
        $prepayment = $prepaymentRepository->getPrepaymentByPrepaymentId($prepaymentId);
        $this->checkPrepaymentBeforeSavePrepaymentEkuaibaoFlowCode($prepayment);

        //获取易快报单据信息
        $ekuaibaoFlowService = new EkuaibaoFlowService();
        $flowDetail = $ekuaibaoFlowService->getFlowDetailByFlowCode($ekuaibaoFlowCode);

        //校验易快报单据信息
        $this->checkFlowBeforeSavePrepaymentEkuaibaoFlowCode($flowDetail, $prepayment);

        //保存易快报单据号
        $flowId = $flowDetail['value']['id'] ?? '';
        $prepaymentRepository->updateEkuaibaoFlowAndOperator($prepaymentId, $ekuaibaoFlowCode, $flowId,
            $this->crmAccountId);
    }

    /**
     * 校验当前操作人是否有效
     * @return void
     * @throws BusinessException
     */
    private function checkCrmAccount()
    {
        if (empty($this->crmAccountId)) {
            throw new BusinessException(MessageConstants::INVALID_OPERATOR);
        }
        $crmAccountRepository = new CrmAccountRepository();
        $crmAccount = $crmAccountRepository->getCrmAccountByCrmAccountId($this->crmAccountId);
        if (empty($crmAccount)) {
            throw new BusinessException(MessageConstants::INVALID_OPERATOR);
        }
    }

    /**
     * 保存易快报编号前校验预存申请
     * @param ProviderPrestore|null $prepayment
     * @return void
     * @throws BusinessException
     */
    private function checkPrepaymentBeforeSavePrepaymentEkuaibaoFlowCode(ProviderPrestore $prepayment = null)
    {
        if (empty($prepayment)) {
            throw new BusinessException(MessageConstants::PREPAYMENT_NOT_EXIST);
        }
        if ($prepayment->ekuaibao_flow_operator_crm_account_id == env('CRM_ACCOUNT_ID_SYSTEM')) {
            throw new BusinessException(MessageConstants::PREPAYMENT_AUTO_PROCESS_CANNOT_SAVE_EKUAIBAO_FLOW_CODE);
        }
        if ($prepayment->status != PrepaymentConstants::STATUS_APPLIED) {
            $statusName = PrepaymentConstants::STATUS_NAME_MAP[$prepayment->status] ?? '';
            throw new BusinessException(sprintf(
                MessageConstants::PREPAYMENT_STATUS_NOT_ALLOW_TO_SAVE_EKUAIBAO_FLOW_CODE, $statusName));
        }
    }

    /**
     * 保存易快报编号前校验易快报单据信息
     * @param $flowDetail
     * @param ProviderPrestore $prepayment
     * @return void
     * @throws BusinessException
     * @throws CurlException
     */
    private function checkFlowBeforeSavePrepaymentEkuaibaoFlowCode($flowDetail, ProviderPrestore $prepayment)
    {
        if (empty($flowDetail)) {
            throw new BusinessException(MessageConstants::EKUAIBAO_FLOW_NOT_EXIST);
        }
        //校验金额相等
        $payMoneyDTO = app(EkuaibaoFlowService::class)->buildPayMoneyDTOBYFlowDetail($flowDetail);
        if (bccomp($payMoneyDTO->getStandard(), $prepayment->amount, 2) != 0) {
            throw new BusinessException(sprintf(MessageConstants::EKUAIBAO_AMOUNT_NOT_EQUAL,
                $payMoneyDTO->getStandard(), $prepayment->amount));
        }

        //校验收款信息一致
        $providerMerchantPaymentService = new ProviderMerchantPaymentService();
        $providerMerchantPaymentService->checkFlowSettleInfoConsistency($flowDetail,
            $prepayment->settleinfojson, $prepayment->settleinfoid);

        //校验是否重复
        $providerMerchantPaymentService->checkDuplicateEkuaibaoFlow($flowDetail);
    }

    /**
     * 创建易快报对公付款单据
     * @param $params
     * @return void
     * @throws BusinessException|CurlException
     */
    public function createEkuaibaoCorporatePaymentFlow($params)
    {
        $prepaymentEkuaibaoCorporatePaymentFlowProcessor = new PrepaymentEkuaibaoCorporatePaymentFlowProcessor([
            'prepaymentId' => $params['prepayment_id'],
            'crmAccountId' => $params['crm_account_id'],
        ]);
        $prepaymentEkuaibaoCorporatePaymentFlowProcessor->process();
    }

    /**
     * 创建易快报预付单据
     * @param $params
     * @return void
     * @throws BusinessException|CurlException
     */
    public function createEkuaibaoPrepaymentFlow($params)
    {
        $prepaymentEkuaibaoPrepaymentFlowProcessor = new PrepaymentEkuaibaoPrepaymentFlowProcessor([
            'prepaymentId' => $params['prepayment_id'],
            'crmAccountId' => $params['crm_account_id'],
        ]);
        $prepaymentEkuaibaoPrepaymentFlowProcessor->process();
    }

    /**
     * @param $prepaymentId
     * @param $action
     * @return bool
     * @throws CurlException
     */
    public function setPrepaymentStatus($prepaymentId, $action): bool
    {
        if (empty($prepaymentId)) {
            return false;
        }
        //更新预存申请状态
        $url = env('CONSTANTS_CRMAPI_URL') . "/supplier-prestore/update-application";
        $params = [
            'prestoreId' => $prepaymentId,
            'action' => $action,
            'admin_account_id' => env('ADMIN_ACCOUNT_ID_SYSTEM'),
        ];
        $originResponse = CurlUtil::postJson($url, $params);
        $response = json_decode($originResponse, true);
        if (isset($response['code']) && $response['code'] == 0) {
            return true;
        } else {
            YqLog::logger("finance:providerMerchantPayment:ekuaibao")
                ->error("更新预存申请状态失败", [
                    'prepaymentId' => $prepaymentId,
                    'response' => $originResponse]);
            return false;
        }
    }
}