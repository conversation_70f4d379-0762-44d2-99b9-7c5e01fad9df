<?php

namespace App\Services\ProviderMerchantPayment\Invoice;

use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Entities\ProviderMerchantPayment\CheckUploadInvoiceFieldsEntity;
use App\Entities\ProviderMerchantPayment\ProviderMerchantInvoiceDTO;
use App\Entities\ProviderMerchantPayment\SettleInfoDTO;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Models\ProviderMerchantInvoice;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantInvoiceRepository;
use App\Utils\StringHelper;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\SettleInfo\PaymentModeConstants;

class BaseSaveInvoicesService
{
    /**
     * @param ProviderMerchantInvoiceDTO[] $invoiceDTOs
     * @param CheckUploadInvoiceFieldsEntity $checkInvoiceFieldsEntity
     * @return void
     * @throws BusinessWithoutErrorReportException
     */
    public function checkUploadInvoiceFields(
        array                          $invoiceDTOs,
        CheckUploadInvoiceFieldsEntity $checkInvoiceFieldsEntity): void
    {
        //取出发票号码相同的旧发票
        $invoiceNumbers = array_map(function ($dto) {
            return $dto->getInvoiceNumber();
        }, $invoiceDTOs);
        $providerMerchantInvoiceRepository = app(ProviderMerchantInvoiceRepository::class);
        $oldInvoices = $providerMerchantInvoiceRepository->getInvoicesByInvoiceNumbers($invoiceNumbers);

        //排除未关联预存或结算的发票
        $oldInvoiceIds = $oldInvoices->pluck('id')->toArray();
        $relatedToPaymentInvoiceIds = $providerMerchantInvoiceRepository
            ->getRelatedToPaymentInvoiceIdsByInvoiceIds($oldInvoiceIds);
        $oldInvoices = $oldInvoices->whereIn('id', $relatedToPaymentInvoiceIds);

        $oldInvoices = $oldInvoices->keyBy(function (ProviderMerchantInvoice $item) {
            return $item->invoice_number . ($item->invoice_code ? ("-" . $item->invoice_code) : "");
        });
        $invoiceAndCodes = [];
        $currentYear = date('Y');

        foreach ($invoiceDTOs as $dto) {
            $invoiceCode = $dto->getInvoiceCode();
            $invoiceNumber = $dto->getInvoiceNumber();
            $invoiceId = $dto->getInvoiceId();
            $invoiceAndCode = $invoiceNumber . ($invoiceCode ? ("-" . $invoiceCode) : "");

            if ($invoiceId > 0) {
                if (isset($oldInvoices[$invoiceAndCode]) &&
                    $oldInvoices[$invoiceAndCode]->id !== $invoiceId) {
                    throw new BusinessWithoutErrorReportException("发票[{$invoiceAndCode}]已存在，请检查");
                }
            } else {
                if (isset($oldInvoices[$invoiceAndCode])) {
                    throw new BusinessWithoutErrorReportException("发票[{$invoiceAndCode}]已存在，请检查");
                }
            }

            $invoiceSellerName = StringHelper::normalizeBrackets($dto->getSellerName());
            $providerMerchantName = StringHelper::normalizeBrackets(
                $checkInvoiceFieldsEntity->getProviderMerchantName());
            if ($invoiceSellerName !== $providerMerchantName) {
                throw new BusinessWithoutErrorReportException("开票主体[{$invoiceSellerName}]与供应商主体".
                    "[{$providerMerchantName}]不一致");
            }

            if ($dto->getBuyerTaxNumber() != $checkInvoiceFieldsEntity->getCompanyTaxNumber()) {
                throw new BusinessWithoutErrorReportException("发票购方税号[{$dto->getBuyerTaxNumber()}]与签约主体税号" .
                    "[{$checkInvoiceFieldsEntity->getCompanyTaxNumber()}]不一致");
            }

            if ($dto->getBuyerName() !== $checkInvoiceFieldsEntity->getCompanyName()) {
                throw new BusinessWithoutErrorReportException("发票抬头[{$dto->getBuyerName()}]与签约主体".
                    "[{$checkInvoiceFieldsEntity->getCompanyName()}]不一致");
            }

            if (date('Y', strtotime($dto->getInvoiceTime())) !== $currentYear) {
                throw new BusinessWithoutErrorReportException("发票时间[{$dto->getInvoiceTime()}]非当前年份".
                    "[{$currentYear}]");
            }

            //本次上传的发票中不能有重复的发票
            if (in_array($invoiceAndCode, $invoiceAndCodes)) {
                throw new BusinessWithoutErrorReportException("发票[{$invoiceAndCode}]重复，请检查");
            }
            $invoiceAndCodes[] = $invoiceAndCode;
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    public function checkSettleInfoFields(array $settleInfoDTOs)
    {
        $bankAccount = '';
        foreach ($settleInfoDTOs as $key => $settleInfoDTO) {
            if (empty($settleInfoDTO->getBankAccount())) {
                throw new BusinessWithoutErrorReportException(MessageConstants::SETTLE_INFO_NOT_EMPTY);
            }
            if ($key == 0) {
                $bankAccount = $settleInfoDTO->getBankAccount();
                continue;
            }
            if ($settleInfoDTO->getBankAccount() != $bankAccount) {
                throw new BusinessWithoutErrorReportException(MessageConstants::SETTLE_INFO_NOT_SAME);
            }
        }
    }

    /**
     * @param SettleInfoDTO[] $settleInfoDTOs
     * @throws BusinessWithoutErrorReportException
     */
    public function checkPaymentModes(array $settleInfoDTOs)
    {
        $this->checkPaymentMode($settleInfoDTOs[0]->getPaymentMode());
        $paymentModes = array_map(function (SettleInfoDTO $dto) {
            return $dto->getPaymentMode();
        }, $settleInfoDTOs);
        if (count(array_unique($paymentModes)) > 1
        ) {
            throw new BusinessWithoutErrorReportException(
                MessageConstants::SETTLE_PAYMENT_MODE_NOT_ALLOW_UPLOAD_INVOICE);
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    public function checkPaymentMode($paymentMode)
    {
        if ($paymentMode != PaymentModeConstants::CORPORATE) {
            throw new BusinessWithoutErrorReportException(
                MessageConstants::SETTLE_PAYMENT_MODE_NOT_ALLOW_UPLOAD_INVOICE);
        }
    }
}