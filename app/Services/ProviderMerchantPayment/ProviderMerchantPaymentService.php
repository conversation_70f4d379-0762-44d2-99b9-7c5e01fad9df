<?php

namespace App\Services\ProviderMerchantPayment;


use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Repositories\CrmAccount\CrmAccountRepository;
use App\Repositories\ProviderMerchantPayment\InvoiceReconciliationApplicationRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPrepaymentRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantSettleInfoRepository;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoPayeeService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoPayeeBusinessService;
use App\Services\ProviderMerchantPayment\OrderSettle\OrderSettleService;
use App\Services\ProviderMerchantPayment\Prepayment\ProviderMerchantPrepaymentService;
use App\Services\ProviderMerchantPayment\Reconciliation\ProviderMerchantInvoiceReconciliationService;
use Yanqu\YanquPhplib\Exception\CurlException;

class ProviderMerchantPaymentService
{
    /**
     * 保存易快报单号
     * @param $params
     * @return void
     * @throws BusinessException
     * @throws CurlException
     * @throws BusinessWithoutErrorReportException
     */
    public function saveEkuaibaoFlowCode($params)
    {
        if (!empty($params['reconciliation_application_id'])) {
            $providerMerchantInvoiceReconciliationService = new ProviderMerchantInvoiceReconciliationService();
            $providerMerchantInvoiceReconciliationService->saveEkuaibaoFlowCode($params);
        } elseif (!empty($params['prepayment_id'])) {
            app(ProviderMerchantPrepaymentService::class)->saveEkuaibaoFlowCode($params);
        } elseif (!empty($params['order_settle_id'])) {
            app(OrderSettleService::class)->saveEkuaibaoFlowCode($params);
        } else {
            throw new BusinessWithoutErrorReportException(
                MessageConstants::PREPAYMENT_ID_OR_RECONCILIATION_ID_OR_SETTLE_ID_REQUIRED);
        }
    }

    /**
     * @throws BusinessWithoutErrorReportException
     */
    public function checkCrmAccount($crmAccountId)
    {
        if (empty($crmAccountId)) {
            throw new BusinessWithoutErrorReportException(MessageConstants::INVALID_OPERATOR);
        }
        $crmAccountRepository = new CrmAccountRepository();
        $crmAccount = $crmAccountRepository->getCrmAccountByCrmAccountId($crmAccountId);
        if (empty($crmAccount)) {
            throw new BusinessWithoutErrorReportException(MessageConstants::INVALID_OPERATOR);
        }
    }

    /**
     * 校验易快报单据是否重复
     * @param $flowDetail
     * @return void
     * @throws BusinessException
     */
    public function checkDuplicateEkuaibaoFlow($flowDetail)
    {
        $flowId = $flowDetail['value']['id'] ?? '';
        $prepaymentRepository = app(ProviderMerchantPrepaymentRepository::class);
        $prepayment = $prepaymentRepository->getPrepaymentByFlowId($flowId);
        if (!empty($prepayment)) {
            throw new BusinessException(sprintf(MessageConstants::EKUAIBAO_FLOW_ID_DUPLICATE_WITH_PREPAYMENT,
                $prepayment->prestoreid));
        }

        $reconciliationRepository = app(InvoiceReconciliationApplicationRepository::class);
        $reconciliation = $reconciliationRepository->getReconciliationApplicationByFlowId($flowId);
        if (!empty($reconciliation)) {
            throw new BusinessException(sprintf(MessageConstants::EKUAIBAO_FLOW_ID_DUPLICATE_WITH_RECONCILIATION,
                $reconciliation->id));
        }
        $orderSettleRepository = app(OrderSettleRepository::class);
        $orderSettle = $orderSettleRepository->getOrderSettleByFlowId($flowId);
        if (!empty($orderSettle)) {
            throw new BusinessException(sprintf(MessageConstants::EKUAIBAO_FLOW_ID_DUPLICATE_WITH_SETTLE,
                $orderSettle->settleid));
        }
    }

    /**
     * 校验单据的收款信息与系统一致
     * @param $flowDetail
     * @param $settleInfosJson
     * @param $settleInfoId
     * @return void
     * @throws BusinessException|CurlException|BusinessWithoutErrorReportException
     */
    public function checkFlowSettleInfoConsistency($flowDetail, $settleInfosJson, $settleInfoId)
    {
        $payeeId = $flowDetail['value']['form']['payeeId'] ?? '';
        $payeeInfo = app(EkuaibaoPayeeService::class)->getPayeeInfo('', $payeeId);
        $ekuaibaoPayeeBusinessService = new EkuaibaoPayeeBusinessService();
        $payeeInfo = $ekuaibaoPayeeBusinessService->buildPayeeInfoDTO($payeeInfo);
        $settleInfo = json_decode($settleInfosJson, true);
        if (empty($settleInfo)) {
            $settleInfo = app(ProviderMerchantSettleInfoRepository::class)->getSettleInfoById($settleInfoId);
            $settleInfo = empty($settleInfo) ? [] : $settleInfo->toArray();
        }
        $bankAccount = $settleInfo['bankaccount'] ?? '';
        if (empty($bankAccount)) {
            throw new BusinessWithoutErrorReportException(MessageConstants::SYSTEM_RECEIVE_CARD_NO_IS_EMPTY);
        }
        //目前只用校验卡号
        if ($bankAccount != $payeeInfo->getCardNo()) {
            $message = sprintf(MessageConstants::EKUAIBAO_PAYEE_CARD_NO_NOT_EQUAL, $payeeInfo->getCardNo(),
                $bankAccount);
            throw new BusinessException($message);
        }
    }
}