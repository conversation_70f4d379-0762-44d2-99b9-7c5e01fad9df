<?php

namespace App\Services\ProviderMerchantPayment\Common\EkuaibaoFlow;

use App\Entities\ProviderMerchantPayment\CreateFlowProcessorEntity;
use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Services\ProviderMerchantPayment\OrderSettle\Ekuaibao\CreateCorporatePaymentFlowProcessor;
use App\Services\ProviderMerchantPayment\OrderSettle\Ekuaibao\CreatePrepaymentFlowProcessor;
use App\Services\ProviderMerchantPayment\OrderSettle\Ekuaibao\CreateReconciliationFlowProcessor;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\EkuaibaoFlowCreatorConstants;

class FlowProcessorFactory
{
    /**
     * @throws BusinessException
     * @throws BusinessWithoutErrorReportException
     */
    public static function createFlowProcessor(CreateFlowProcessorEntity $params): CreateFlowProcessorInterface
    {
        switch ($params->getApplicationType()) {
            case EkuaibaoFlowCreatorConstants::APPLICATION_TYPE_ORDER_SETTLE:
                switch ($params->getFlowType()) {
                    case EkuaibaoFlowCreatorConstants::FLOW_TYPE_CORPORATE_PAYMENT:
                        return new CreateCorporatePaymentFlowProcessor([
                            'crmAccountId' => $params->getCrmAccountId(),
                            'orderSettleId' => $params->getOrderSettleId(),
                        ]);
                    case EkuaibaoFlowCreatorConstants::FLOW_TYPE_PREPAYMENT:
                        return new CreatePrepaymentFlowProcessor([
                            'crmAccountId' => $params->getCrmAccountId(),
                            'orderSettleId' => $params->getOrderSettleId(),
                        ]);
                    case EkuaibaoFlowCreatorConstants::FLOW_TYPE_RECONCILIATION:
                        return new CreateReconciliationFlowProcessor([
                            'crmAccountId' => $params->getCrmAccountId(),
                            'reconciliationId' => $params->getReconciliationId(),
                        ]);
                    default:
                        throw new BusinessException('Invalid flow type: ' . $params['flow_type']);
                }
            default:
                throw new BusinessException('Invalid application type: ' . $params['application_type']);
        }
    }
}