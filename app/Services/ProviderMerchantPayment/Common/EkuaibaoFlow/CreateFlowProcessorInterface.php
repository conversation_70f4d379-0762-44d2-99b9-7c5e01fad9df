<?php

namespace App\Services\ProviderMerchantPayment\Common\EkuaibaoFlow;

use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use Yanqu\YanquPhplib\Exception\CurlException;

interface CreateFlowProcessorInterface
{
    /**
     * @throws BusinessException
     * @throws BusinessWithoutErrorReportException
     * @throws CurlException
     */
    public function process();
}