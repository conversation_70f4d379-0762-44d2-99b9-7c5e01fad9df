<?php

namespace App\Services\ProviderMerchantPayment\EkuaibaoFlow;

use App\Constants\Ekuaibao\CustomDimension\EkuaibaoCustomDimensionConstants as CustomDimensionConstants;
use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants as ReconciliationConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantPaymentErrorLogConstants;
use App\Entities\Ekuaibao\FlowForm\LoanWrittenOffDTO;
use App\Entities\Ekuaibao\FlowForm\PrepaymentReconciliationFlowFormDTO;
use App\Entities\ProviderMerchantPayment\ProviderMerchantInvoiceDTO;
use App\Exceptions\BusinessException;
use App\Models\ProviderMerchantInvoiceReconciliationApplication as Reconciliation;
use App\Repositories\ProviderMerchant\ProviderMerchantRepository;
use App\Repositories\ProviderMerchantPayment\InvoiceReconciliationApplicationRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantInvoiceRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPaymentErrorLogRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantPrepaymentRepository as PrepaymentRepository;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFeeTypeService;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoFlowService;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoLoansService;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoMoneyService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoFlowBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoInvoiceBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoRecordLinkBusinessService;
use App\Services\Ekuaibao\EkuaibaoBusiness\EkuaibaoStaffBusinessService;
use Illuminate\Database\Eloquent\Collection;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\YqLog\YqLog;

class EkuaibaoPrepaymentReconciliationFlowProcessor extends BaseEkuaibaoFlowProcessor
{
    private $reconciliationId;
    private $crmAccountId;
    /**
     * @var ProviderMerchantInvoiceDTO[]
     */
    private $invoiceDTOs;

    public function __construct($data)
    {
        if (empty($data['reconciliationApplicationId']) || empty($data['crmAccountId'])) {
            throw new BusinessException("reconciliationApplicationId 和 crmAccountId 是必填的");
        }
        $this->reconciliationId = $data['reconciliationApplicationId'];
        $this->crmAccountId = $data['crmAccountId'];
    }

    /**
     * @return void
     * @throws BusinessException
     * @throws CurlException
     */
    public function process()
    {
        //获取并设置发票信息
        $invoiceReconciliationApplicationRepository = app(InvoiceReconciliationApplicationRepository::class);
        $invoiceIds = $invoiceReconciliationApplicationRepository
            ->getInvoiceIdsByReconciliationId($this->reconciliationId);
        $invoices = app(ProviderMerchantInvoiceRepository::class)->getInvoicesByInvoiceIds($invoiceIds);
        $invoiceDTOs = [];
        foreach ($invoices as $invoice) {
            $invoiceDTOs[] = new ProviderMerchantInvoiceDTO($invoice);
        }
        $this->invoiceDTOs = $invoiceDTOs;

        //获取核销信息
        $reconciliation = $invoiceReconciliationApplicationRepository
            ->getReconciliationApplicationById($this->reconciliationId);

        //校验核销信息
        $this->checkReconciliation($reconciliation);

        //获取关联的预存申请
        $prepaymentIds = $invoiceReconciliationApplicationRepository
            ->getPaymentRelationsByReconciliationId($this->reconciliationId)
            ->where('relation_type', ReconciliationConstants::RELATE_TO_PAYMENT_TYPE_PREPAYMENT)
            ->pluck('related_application_id')->toArray();
        $prepaymentRepository = app(PrepaymentRepository::class);
        $prepayments = $prepaymentRepository->getPrepaymentsByPrepaymentIds($prepaymentIds);

        //构建表单
        $prepaymentReconciliationFlowFormDTO = $this->buildPrepaymentReconciliationFlowFormDTO($reconciliation,
            $prepayments);

        //创建单据
        $creatFlowResult = app(EkuaibaoFlowService::class)
            ->createPrepaymentReconciliationFlow($prepaymentReconciliationFlowFormDTO);

        //处理错误
        $hasError = $this->handleFlowCreationError($creatFlowResult);

        if (!$hasError) {
            //更新单据编号和易快报单据操作人
            $ekuaibaoFlowId = $creatFlowResult['flow']['id'];
            $ekuaibaoFlowCode = $creatFlowResult['flow']['form']['code'];
            $invoiceReconciliationApplicationRepository->updateEkuaibaoFlowAndOperator($this->reconciliationId,
                $ekuaibaoFlowCode, $ekuaibaoFlowId, env('CRM_ACCOUNT_ID_SYSTEM'));
        }
    }

    /**
     * @param Reconciliation|null $reconciliation
     * @return void
     * @throws BusinessException
     */
    private function checkReconciliation(Reconciliation $reconciliation = null)
    {
        //校验核销信息
        if (empty($reconciliation)) {
            throw new BusinessException(MessageConstants::RECONCILIATION_NOT_EXIST);
        }
        //已经创建过单据不能再次创建
        if (!empty($reconciliation->ekuaibao_flow_code)) {
            throw new BusinessException(MessageConstants::EKUAIBAO_FLOW_CODE_EXIST);
        }
        if ($reconciliation->process_status != ReconciliationConstants::PROCESS_STATUS_TO_BE_REVIEWED) {
            throw new BusinessException(MessageConstants::RECONCILIATION_STATUS_NOT_ALLOW);
        }
    }

    /**
     * @param Reconciliation $reconciliation
     * @param Collection $prepayments
     * @return PrepaymentReconciliationFlowFormDTO
     * @throws CurlException|BusinessException
     */
    private function buildPrepaymentReconciliationFlowFormDTO(
        Reconciliation $reconciliation, Collection $prepayments): PrepaymentReconciliationFlowFormDTO
    {
        //准备数据
        $ekuaibaoFlowBusinessService = app(EkuaibaoFlowBusinessService::class);
        $ekuaibaoStaffBusinessService = app(EkuaibaoStaffBusinessService::class);
        $ekuaibaoRecordLinkBusinessService = app(EkuaibaoRecordLinkBusinessService::class);
        $specificationId = $ekuaibaoFlowBusinessService->getPrepaymentReconciliationSpecificationId();
        $providerMerchantRepository = app(ProviderMerchantRepository::class);
        $providerMerchants = $providerMerchantRepository
            ->getProviderMerchantsByProviderMerchantIds($prepayments->pluck('providermerchantid')->toArray());
        $providerMerchantContactNames = $providerMerchants->pluck('contacter')->toArray();
        $title = sprintf(ReconciliationConstants::EKUAIBAO_FLOW_TITLE, '预存', $reconciliation->id,
            implode('、', $providerMerchantContactNames));
        $ekuaibaoStaffDTO = $ekuaibaoStaffBusinessService->getStaffDTOByCrmAccountId($this->crmAccountId);
        $departmentAttribute = $ekuaibaoRecordLinkBusinessService->getDepartmentAttributeIdByDepartmentId(
            $ekuaibaoStaffDTO->getDefaultDepartment());
        $payeeId = $this->getPayeeId($reconciliation->settle_infos_json);
        $description = ReconciliationConstants::EKUAIBAO_FLOW_DESCRIPTION;
        $formDetailDTO = $this->getPrepaymentReconciliationFormDetailDTO($reconciliation);
        $loanWrittenOffs = $this->getLoadWrittenOff($prepayments->pluck('ekuaibao_flow_id')->toArray(),
            $reconciliation->reconciliation_amount);

        //构建表单
        $prepaymentReconciliationFlowFormDTO = new PrepaymentReconciliationFlowFormDTO;
        $prepaymentReconciliationFlowFormDTO->setSpecificationId($specificationId);
        $prepaymentReconciliationFlowFormDTO->setTitle($title);
        $prepaymentReconciliationFlowFormDTO->setLegalEntity($reconciliation->ekuaibao_company_code);
        $prepaymentReconciliationFlowFormDTO->setSubmitterId($ekuaibaoStaffDTO->getId());
        $prepaymentReconciliationFlowFormDTO->setExpenseDepartment($ekuaibaoStaffDTO->getDefaultDepartment());
        $prepaymentReconciliationFlowFormDTO->setDepartmentAttribute($departmentAttribute);
        $prepaymentReconciliationFlowFormDTO->setPayeeId($payeeId);
        $prepaymentReconciliationFlowFormDTO->setDescription($description);
        $prepaymentReconciliationFlowFormDTO->setDetails([$formDetailDTO]);
        $prepaymentReconciliationFlowFormDTO->setLoanWrittenOff($loanWrittenOffs);
        return $prepaymentReconciliationFlowFormDTO;
    }

    /**
     * @param Reconciliation $reconciliation
     * @return mixed
     * @throws BusinessException
     * @throws CurlException
     */
    private function getPrepaymentReconciliationFormDetailDTO(Reconciliation $reconciliation)
    {
        $feeTypeId = app(EkuaibaoFeeTypeService::class)->getCostTestPlatformId();
        $feTypeSpecificationId = $this->getFeeTypeSpecificationId();
        $invoiceForm = app(EkuaibaoInvoiceBusinessService::class)
            ->getInvoiceFormDTOByProviderMerchantInvoices($this->invoiceDTOs);
        $feeTypeFormAmount = app(EkuaibaoMoneyService::class)
            ->buildMoneyDTOByRmbAmount($reconciliation->reconciliation_amount);
        return $this->getFormDetailDTO($feeTypeFormAmount, $feeTypeId, $feTypeSpecificationId, $invoiceForm);
    }


    /**
     * @throws CurlException
     */
    private function getFeeTypeSpecificationId()
    {
        $ekuaibaoFeeTypeService = app(EkuaibaoFeeTypeService::class);
        $feeTypeDetail = $ekuaibaoFeeTypeService
            ->getFeeTypeDetailByIdsAndCodes([$ekuaibaoFeeTypeService->getCostTestPlatformId()]);
        return $feeTypeDetail[0]['expenseSpecificationId'] ?? '';
    }

    /**
     * @param $creatFlowResult
     * @return bool
     * @throws CurlException
     */
    private function handleFlowCreationError($creatFlowResult): bool
    {
        if (isset($creatFlowResult['flow']['id']) && isset($creatFlowResult['flow']['form']['code'])) {
            return false;
        }

        $errorMessage = is_array($creatFlowResult) ? json_encode($creatFlowResult) : (string)$creatFlowResult;
        YqLog::logger("finance:providerMerchantPayment:ekuaibaoFlow")->warning($errorMessage,[
            'prepaymentId' => $this->reconciliationId,
            'creatFlowResult' => $creatFlowResult
        ]);

        $errorLog = [
            'application_type' => ProviderMerchantPaymentErrorLogConstants::APPLICATION_TYPE_INVOICE_RECONCILIATION,
            'related_application_id' => $this->reconciliationId,
            'crm_account_id' => $this->crmAccountId,
            'error_content' => $errorMessage,
            'step' => ProviderMerchantPaymentErrorLogConstants::STEP_CREATE_RECONCILIATION,
        ];
        app(ProviderMerchantPaymentErrorLogRepository::class)->insertErrorLog($errorLog);
        return true;
    }
}