<?php

namespace App\Services\ProviderMerchantPayment\EkuaibaoFlow;

use App\Constants\ProviderMerchantPayment\MessageConstants;
use App\Entities\Ekuaibao\Detail\FormDetailDTO;
use App\Entities\Ekuaibao\FeeTypeForm\FeeTypeFormDTO;
use App\Entities\Ekuaibao\PayeeInfoDTO;
use App\Entities\ProviderMerchantPayment\ProviderMerchantInvoiceDTO;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Repositories\ProviderMerchantPayment\InvoiceReconciliationApplicationRepository as ReconciliationRepository;
use App\Repositories\ProviderMerchantPayment\OrderSettleRepository;
use App\Repositories\ProviderMerchantPayment\ProviderMerchantInvoiceRepository;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoLoansService;
use App\Services\Ekuaibao\EkuaibaoApi\EkuaibaoPayeeService;
use Yanqu\YanquPhplib\Exception\CurlException;
use Yanqu\YanquPhplib\Openapi\ProviderMerchantPayment\Constants\EkuaibaoFlowCreatorConstants;

class BaseEkuaibaoFlowProcessor
{
    /**
     * @param $settleInfoJson
     * @return mixed|string
     * @throws CurlException
     */
    public function getPayeeId($settleInfoJson)
    {
        $settleInfo = json_decode($settleInfoJson, true);
        $bankCardNo = $settleInfo['bankaccount'] ?? '';
        if (empty($bankCardNo)) {
            return '';
        }
        $payeeInfo = app(EkuaibaoPayeeService::class)->getPayeeInfo($bankCardNo);
        return $payeeInfo['id'] ?? '';
    }

    /**
     * @throws CurlException
     */
    public function getPayeeInfo($settleInfoJson): PayeeInfoDTO
    {
        $settleInfo = json_decode($settleInfoJson, true);
        $bankCardNo = $settleInfo['bankaccount'] ?? '';
        if (empty($bankCardNo)) {
            return new PayeeInfoDTO();
        }
        $payeeInfo = app(EkuaibaoPayeeService::class)->getPayeeInfo($bankCardNo);
        return new PayeeInfoDTO($payeeInfo);
    }


    /**
     * @param $feeTypeFormAmount
     * @param $feeTypeId
     * @param $feTypeSpecificationId
     * @param $invoiceForm
     * @return FormDetailDTO
     */
    public function getFormDetailDTO($feeTypeFormAmount, $feeTypeId, $feTypeSpecificationId, $invoiceForm):
    FormDetailDTO
    {
        $formDetailDTO = new FormDetailDTO();
        $formDetailDTO->setFeeTypeId($feeTypeId);
        $formDetailDTO->setSpecificationId($feTypeSpecificationId);
        $feeTypeForm = new FeeTypeFormDTO();
        $feeTypeForm->setAmount($feeTypeFormAmount);
        $feeTypeForm->setFeeDate(round(microtime(true) * 1000));
        $feeTypeForm->setInvoiceForm($invoiceForm);
        $formDetailDTO->setFeeTypeForm($feeTypeForm);
        $feeTypeForm->setBackupAmount($feeTypeFormAmount);
        return $formDetailDTO;
    }

    /**
     * @return ProviderMerchantInvoiceDTO[]
     * @throws BusinessWithoutErrorReportException
     */
    public function loadInvoiceDTOs($step, $id): array
    {
        if ($step == EkuaibaoFlowCreatorConstants::STEP_AUDIT_SETTLEMENT_BY_SUPPLIER_SPECIALIST) {
            $invoiceIds = app(OrderSettleRepository::class)->getInvoiceIdsBySettleId($id);
        } elseif ($step == EkuaibaoFlowCreatorConstants::STEP_CREATE_SETTLEMENT_RECONCILIATION) {
            $invoiceIds = app(ReconciliationRepository::class)->getInvoiceIdsByReconciliationId($id);
        } else {
            throw new BusinessWithoutErrorReportException(
                sprintf(MessageConstants::EKUAIBAO_FLOW_STEP_NOT_SUPPORT, $step));
        }
        $invoices = app(ProviderMerchantInvoiceRepository::class)->getInvoicesByInvoiceIds($invoiceIds);
        $invoiceDTOs = [];
        foreach ($invoices as $invoice) {
            $invoiceDTOs[] = new ProviderMerchantInvoiceDTO($invoice);
        }
        return $invoiceDTOs;
    }

    /**
     * @param int[] $flowIds 原申请的单据id
     * @param $writtenOffAmountSum
     * @return array
     * @throws CurlException
     */

    public function getLoadWrittenOff(array $flowIds, $writtenOffAmountSum): array
    {
        $loadWrittenOffs = [];
        $ekuaibaoLoansService = app(EkuaibaoLoansService::class);
        foreach ($flowIds as $flowId) {
            $loadInfo = $ekuaibaoLoansService->getLoanInfoByFlowId($flowId);
            $loanWrittenOffDTO = $ekuaibaoLoansService->buildLoanWrittenOffDTOByLoanInfo($loadInfo);
            $writtenOffAmount = min($loanWrittenOffDTO->getAmount(), $writtenOffAmountSum);
            $writtenOffAmountSum -= $writtenOffAmount;
            $loanWrittenOffDTO->setAmount($writtenOffAmount);
            $loadWrittenOffs[] = $loanWrittenOffDTO;
        }
        return $loadWrittenOffs;
    }
}