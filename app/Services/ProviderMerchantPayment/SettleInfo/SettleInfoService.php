<?php

namespace App\Services\ProviderMerchantPayment\SettleInfo;

use App\Entities\ProviderMerchantPayment\SettleInfoDTO;
use App\Models\ProviderMerchantSettleInfo;

class SettleInfoService
{
    public function buildSettleInfoDTO($settleInfo): SettleInfoDTO
    {
        $dtoData = [
            'merchantSettleInfoId' => $settleInfo['merchantsettleinfoid'] ?? null,
            'providerMerchantId' => $settleInfo['providermerchantid'] ?? null,
            'paymentMode' => $settleInfo['paymentmode'] ?? null,
            'paymentMethod' => $settleInfo['paymentmethod'] ?? null,
            'alipayAccount' => $settleInfo['alipayaccount'] ?? null,
            'alipayName' => $settleInfo['alipayname'] ?? null,
            'wechatPay' => $settleInfo['wechatpay'] ?? null,
            'openingBank' => $settleInfo['openingbank'] ?? null,
            'bankAccount' => $settleInfo['bankaccount'] ?? null,
            'bankName' => $settleInfo['bankname'] ?? null,
            'bankCode' => $settleInfo['bankcode'] ?? null,
            'invoicePaymentOrder' => $settleInfo['winfksort'] ?? null,
            'tax' => $settleInfo['tax'] ?? null,
            'idCard' => $settleInfo['idcard'] ?? null,
            'isVoid' => $settleInfo['isvoid'] ?? null,
            'invoiceTax' => $settleInfo['invoice_tax'] ?? null,
            'companyId' => $settleInfo['company_id'] ?? null,
            'paymentPeriod' => $settleInfo['payment_period'] ?? null,
            'ticketType' => $settleInfo['ticketype'] ?? null,
            'payeeMobile' => $settleInfo['payee_mobile'] ?? null,
        ];
        return new SettleInfoDTO($dtoData);
    }

    public function buildSettleInfoDTOsByCollection($settleInfos): array
    {
        $settleInfoDTOs = [];
        /** @var $settleInfo ProviderMerchantSettleInfo */
        foreach ($settleInfos as $settleInfo) {
            $settleInfoDTOs[] = $this->buildSettleInfoDTO($settleInfo->toArray());
        }
        return $settleInfoDTOs;
    }
}