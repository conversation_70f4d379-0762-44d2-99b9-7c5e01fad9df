<?php

namespace App\Console\Commands;

use App\Models\AutoInvoice;
use App\Models\QueueTask;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Vtiful\Kernel\Excel;
use Yanqu\YanquPhplib\YqLog\YqLog;

abstract class AbstractImportInvoiceCommand extends Command
{
    // 错误信息收集
    protected $errorMessages = [];

    // 子类需要定义的常量和属性
    abstract protected function getTaskName(): string;
    abstract protected function getModelHeadRef(): array;
    abstract protected function getModelHeadRefMap(): array;
    abstract protected function getUnRequireRef(): array;

    // 子类需要实现的方法
    abstract protected function processRowSpecific(array $row, int $timestamp): ?array;
    abstract protected function validateSpecialFields(array $row): bool;
    abstract protected function postProcessData(Collection $data): Collection;

    public function handle(): void
    {
        $tasks = $this->getPendingTasks();
        foreach ($tasks as $task) {
            $this->processTask($task);
        }
    }

    protected function getPendingTasks(): Collection
    {
        return QueueTask::query()
            ->where('taskname', $this->getTaskName())
            ->where('queuestatus', 0)
            ->get();
    }

    protected function processTask(QueueTask $task): void
    {
        // 初始化错误信息收集
        $this->errorMessages = [];
        
        try {
            // 下载Excel文件
            $filename = $this->downloadExcel($task);
            if (!$filename) {
                $this->logError('文件下载失败', ['task_id' => $task->id]);
                throw new \RuntimeException('文件下载失败');
            }

            //判断是否是xlsx文件
            if (pathinfo($filename, PATHINFO_EXTENSION) !== 'xlsx') {
                $this->logError('文件格式不正确', ['task_id' => $task->id]);
                throw new \RuntimeException('文件格式不正确');
            }

            // 处理Excel数据
            $result = $this->processExcelFile($filename);
            
            // 将错误信息添加到结果中
            $result['error_messages'] = $this->errorMessages;

            // 更新任务状态
            $this->updateTaskStatus($task, $result);
        } catch (\Exception $e) {
            $this->handleTaskError($task, $e);
        } finally {
            // 清理临时文件
            if (isset($filename)) {
                Storage::disk('local')->delete($filename);
            }
        }
    }

    protected function downloadExcel(QueueTask $task): ?string
    {
        $taskArgs = json_decode($task->args, true);
        $ossUrl = $taskArgs['ossUrl'];
        $filename = basename($ossUrl);

        $client = new Client();
        $response = $client->get($ossUrl);

        if ($response->getStatusCode() !== 200) {
            return null;
        }

        Storage::disk('local')->put($filename, $response->getBody());
        return $filename;
    }

    protected function processExcelFile(string $filename): array
    {
        $filePath = storage_path('app');
        $excel = new Excel(['path' => $filePath]);
        $excel->openFile($filename)->openSheet();

        // 读取并验证表头
        $headerRow = $excel->nextRow();
        if (!$this->validateHeaders($headerRow)) {
            return [
                'total' => 0,
                'success' => 0,
                'error' => 0,
                'duplicate' => 0,
                'failed' => true
            ];
        }

        $data = new Collection();
        $totalCount = 0;
        $timestamp = Carbon::now()->timestamp;
        $importFailed = false;
        $message = '';

        // 处理数据行
        while (($row = $excel->nextRow()) !== null && !empty($row[0])) {
            $totalCount++;
            $processedRow = $this->processRow($row, $timestamp);

            // 如果任何一行处理失败，标记整个导入失败
            if ($processedRow === null) {
                $importFailed = true;
                break;
            }

            $data->push($processedRow);
        }

        // 如果导入失败，返回失败状态
        if ($importFailed) {
            return [
                'total' => $totalCount,
                'success' => 0,
                'error' => $totalCount,
                'duplicate' => 0,
                'failed' => true
            ];
        }

        // 处理重复数据
        $data = $this->removeDuplicates($data);

        // 过滤已存在的发票
        $uniqueData = $this->filterExistingInvoices($data);

        // 子类特定的后处理（如地区处理）
        $processedData = $this->postProcessData($uniqueData);

        // 保存数据
        $successCount = $this->saveInvoices($processedData);

        return [
            'total' => $totalCount,
            'success' => $successCount,
            'error' => $totalCount - $data->count(),
            'duplicate' => $data->count() - $successCount,
            'failed' => false
        ];
    }

    protected function validateHeaders(array $headerRow): bool
    {
        $modelHeader = $this->getModelHeadRef();

        foreach ($headerRow as $key => $value) {
            if (!empty($value)) {
                if (!isset($modelHeader[$key]) || $modelHeader[$key] !== $value) {
                    $this->logError(
                        "表头{$value}与预期{$modelHeader[$key]}不匹配, 请检查文件格式",
                        ['headerRow' => $headerRow]
                    );
                    $this->errorMessages[] = [
                        'message' => "表头{$value}与预期{$modelHeader[$key]}不匹配, 请检查文件格式",
                        'row_data' => $headerRow
                    ];
                    return false;
                }
            }
        }

        return true;
    }

    protected function processRow(array $row, int $timestamp): ?array
    {
        $processed = [];
        $modelHeadRef = $this->getModelHeadRef();
        $modelHeadRefMap = $this->getModelHeadRefMap();
        $unRequireRef = $this->getUnRequireRef();

        // 处理每个字段
        foreach ($row as $key => $value) {
            if (!isset($modelHeadRef[$key])) {
                break;
            }

            $columnName = $modelHeadRefMap[$key];
            $value = trim($value);

            // 验证必填字段
            if (empty($value) && !isset($unRequireRef[$columnName])) {
                $this->logError("字段 {$columnName} 不能为空", $row);
                return null;
            }

            // 通用字段处理
            if ($columnName == 'amount') {
                $value = $this->dealAmount($value);
            }

            if ($columnName == 'invoiceno') {
                $value = str_replace([";", "；", "、", "\\"], ",", $this->excelTrim($value));
            }

            if ($columnName == 'title') {
                $value = str_replace('　', ' ', $value);
                $value = str_replace("  ", ' ', $value);
            }

            $processed[$columnName] = $value;
        }

        // 子类特定的字段处理
        $processed = $this->processRowSpecific($processed, $timestamp);
        if ($processed === null) {
            return null;
        }

        // 特殊字段处理
        if (!$this->validateSpecialFields($processed)) {
            return null;
        }

        // 通用字段处理
        $processed['date'] = strtotime($processed['date']);
        $processed['amount'] = $processed['amount'] ?? 0;
        $processed['unmatchamount'] = $processed['amount'];

        // 处理备注
        $processed['remark'] = json_encode([[
            'accountid' => -9999,
            'savetime' => $timestamp,
            'content' => $processed['remark'] ?? '',
        ]]);

        // 添加通用额外字段
        $processed['create'] = $timestamp;
        $processed['update'] = $timestamp;
        $processed['state'] = 0;

        return $processed;
    }

    protected function removeDuplicates(Collection $data): Collection
    {
        return $data->unique('serialno')->values();
    }

    protected function filterExistingInvoices(Collection $data): Collection
    {
        $importSerialnos = $data->pluck('serialno');
        $existingSerialnos = AutoInvoice::query()
            ->whereIn('serialno', $importSerialnos)
            ->pluck('date', 'serialno');

        return $data->reject(function ($item) use ($existingSerialnos) {
            return $existingSerialnos->has($item['serialno']);
        });
    }

    protected function saveInvoices(Collection $data): int
    {
        if ($data->isEmpty()) {
            return 0;
        }

        AutoInvoice::insert($data->toArray());
        return $data->count();
    }

    protected function updateTaskStatus(QueueTask $task, array $result): void
    {
        if (isset($result['failed']) && $result['failed']) {
            // 导入失败
            $task->queuestatus = 2;
            $task->success_num = 0;
            $task->num = $result['total'];
            $task->error_num = $result['total'];
            $task->double_num = 0;
        } else {
            // 导入成功
            $task->queuestatus = 1;
            $task->success_num = $result['success'];
            $task->num = $result['total'];
            $task->error_num = $result['error'];
            $task->double_num = $result['duplicate'];
        }

        // 保存错误信息到 log 字段
        if (isset($result['error_messages'][0]['message']) && !empty($result['error_messages'][0]['message']) && $task->queuestatus == 2) {
            $task->log = $result['error_messages'][0]['message'];
        }

        $task->save();
    }

    protected function handleTaskError(QueueTask $task, \Exception $e): void
    {
        $task->queuestatus = 2; // 导入失败
        $task->success_num = 0;
        $task->num = 0;
        $task->error_num = 0;
        $task->double_num = 0;
        
        // 保存异常信息到 log 字段
        $task->log = empty($this->errorMessages[0]['message']) ? '' : $this->errorMessages[0]['message'];
        
        $task->save();

        YqLog::logger('finance:invoice:import')->error(
            "任务处理失败: {$e->getMessage()}",
            ['task_id' => $task->id, 'exception' => $e]
        );
    }

    protected function logError(string $message, array $row): void
    {
        $serialno = $row['serialno'] ?? '未知';
        
        // 收集错误信息
        $this->errorMessages[] = [
            'serialno' => $serialno,
            'message' => $message,
            'row_data' => $row
        ];
        
        YqLog::logger('finance:invoice:import')->info(
            "流水号:{$serialno} 导入失败, {$message}",
            ['row' => $row]
        );
    }

    // 通用工具方法
    public function excelTrim($string)
    {
        return preg_replace("/(\s|\&nbsp\;|　|\xc2\xa0|`)/", "", $string);
    }

    public function dealAmount($amount)
    {
        return str_replace("，", "", str_replace(",", "", $this->excelTrim($amount)));
    }
}
