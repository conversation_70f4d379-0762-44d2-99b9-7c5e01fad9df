<?php

namespace App\Console\Commands;

use App\Models\AutoInvoice;
use App\Models\Company;
use App\Models\Hospital;
use App\Models\QueueTask;
use App\Models\TitleUnivsRef;
use App\Models\Univs;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Vtiful\Kernel\Excel;
use Yanqu\YanquPhplib\YqLog\YqLog;

class ImportAutoInvoiceCommand extends AbstractImportInvoiceCommand
{
    protected $signature = 'service:ImportAutoInvoice';

    protected $description = '导入流水';

    protected function getTaskName(): string
    {
        return 'invoiceUpload';
    }

    protected function getModelHeadRef(): array
    {
        return [
            '银行流水号',
            '到账日期',
            '发票抬头',
            '到账银行',
            '打款银行',
            '金额',
            '收款公司账户名',
            '附言',
            '发票号',
            '备注'
        ];
    }

    protected function getModelHeadRefMap(): array
    {
        return [
            'serialno',
            'date',
            'title',
            'arriving_bank',
            'payment_bank',
            'amount',
            'companyname',
            'postscript',
            'invoiceno',
            'remark',
        ];
    }

    protected function getUnRequireRef(): array
    {
        return [
            'remark' => 1,
            'postscript' => 1,
            'invoiceno' => 1,
        ];
    }

    protected function processRowSpecific(array $row, int $timestamp): ?array
    {
        // 国内流水没有特殊的行处理逻辑
        return $row;
    }

    protected function validateSpecialFields(array $row): bool
    {
        if (
            strtotime($row['date']) < strtotime('2000-01-01')
            || strtotime($row['date']) >= strtotime('2999-01-01')
        ) {
            $this->logError("到账日期不合法: {$row['date']}", $row);
            $this->errorMessages[] = [
                'message' => "到账日期不合法: {$row['date']}",
                'row_data' => $row
            ];
            return false;
        }

        if (!is_numeric($row['amount'])) {
            $this->logError("金额不合法: {$row['amount']}", $row);
            $this->errorMessages[] = [
                'message' => "金额不合法: {$row['amount']}",
                'row_data' => $row
            ];
            return false;
        }

        return true;
    }

    protected function postProcessData(Collection $data): Collection
    {
        // 根据打款账号查找其所在地区
        return $this->dealRegion($data);
    }

    /**
     * @param Collection $data
     * @return Collection
     * <AUTHOR>
     */
    public function dealRegion(Collection $data): Collection
    {
        // 获取所有唯一的抬头
        $titles = $data->pluck('title')->unique()->values();

        // 批量查询抬头映射关系
        $reflectionTitle = TitleUnivsRef::query()
            ->whereIn('title', $titles)
            ->pluck('invoicetitle', 'title');

        // 合并原始抬头和映射后的抬头
        $allTitles = $titles->merge($reflectionTitle)->unique()->values();
        $reflectionTitleFlipped = $reflectionTitle->flip();

        // 批量查询所有数据源的地区信息
        $regionInfo = $this->batchQueryRegionInfo($allTitles);

        // 构建最终的地区映射关系
        $finalRegionMap = $this->buildFinalRegionMap($titles, $reflectionTitle, $reflectionTitleFlipped, $regionInfo);

        // 为数据添加地区信息
        return $this->attachRegionToData($data, $finalRegionMap);
    }

    private function batchQueryRegionInfo(Collection $titles): array
    {
        $regionInfo = [];
        $trimmedTitles = $titles->map(function ($title) {
            return $this->excelTrim($title);
        });

        // 批量查询大学信息
        $univsData = Univs::query()
            ->whereIn('univsname', $trimmedTitles)
            ->select(['univsname', 'provinceid', 'cityid'])
            ->get()
            ->keyBy('univsname');

        // 批量查询医院信息
        $hospitalsData = Hospital::query()
            ->whereIn('hname', $trimmedTitles)
            ->select(['hname', 'province', 'city'])
            ->get()
            ->keyBy('hname');

        // 批量查询公司信息
        $companiesData = Company::query()
            ->whereIn('company_name', $trimmedTitles)
            ->where('is_standard', 0)
            ->select(['company_name', 'province', 'city'])
            ->get()
            ->keyBy('company_name');

        // 整理地区信息，按优先级：大学 > 医院 > 公司
        foreach ($titles as $title) {
            $trimmedTitle = $this->excelTrim($title);

            // 优先查找大学
            if ($univsData->has($trimmedTitle)) {
                $univInfo = $univsData[$trimmedTitle];
                $regionInfo[$title] = [
                    'province_id' => $univInfo->provinceid,
                    'city_id' => $univInfo->cityid,
                ];
                continue;
            }

            // 其次查找医院
            if ($hospitalsData->has($trimmedTitle)) {
                $hospitalInfo = $hospitalsData[$trimmedTitle];
                $regionInfo[$title] = [
                    'province_id' => $hospitalInfo->province,
                    'city_id' => $hospitalInfo->city,
                ];
                continue;
            }

            // 最后查找公司
            if ($companiesData->has($trimmedTitle)) {
                $companyInfo = $companiesData[$trimmedTitle];
                $regionInfo[$title] = [
                    'province_id' => $companyInfo->province,
                    'city_id' => $companyInfo->city,
                ];
            }
        }

        return $regionInfo;
    }

    private function buildFinalRegionMap(
        Collection $originalTitles,
        Collection $reflectionTitle,
        Collection $reflectionTitleFlipped,
        array $regionInfo
    ): array {
        $finalRegionMap = [];

        // 遍历所有抬头（包括原始抬头和映射后的抬头）
        foreach ($regionInfo as $title => $region) {
            // 如果这个抬头是映射后的抬头，需要先检查原抬头是否已经有地区信息
            if ($reflectionTitleFlipped->has($title) && isset($finalRegionMap[$reflectionTitleFlipped[$title]])) {
                continue;
            }

            // 根据是否有映射关系来决定存储到哪个key下
            if ($reflectionTitleFlipped->has($title)) {
                // 这是一个映射后的抬头，存储到原抬头下
                $finalRegionMap[$reflectionTitleFlipped[$title]] = $region;
            } else {
                // 这是一个原始抬头，直接存储
                $finalRegionMap[$title] = $region;
            }
        }

        return $finalRegionMap;
    }

    private function attachRegionToData(Collection $data, array $regionMap): Collection
    {
        return $data->map(function ($row) use ($regionMap) {
            $row['province_id'] = $regionMap[$row['title']]['province_id'] ?? 0;
            $row['city_id'] = $regionMap[$row['title']]['city_id'] ?? 0;
            return $row;
        });
    }
}
