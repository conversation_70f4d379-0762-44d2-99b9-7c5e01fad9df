<?php

namespace App\Console\Commands;

use App\Models\AutoInvoice;
use App\Models\QueueTask;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Vtiful\Kernel\Excel;
use Yanqu\YanquPhplib\Invoice\Constants\InvoiceConstants;
use Yanqu\YanquPhplib\YqLog\YqLog;

class ImportAutoForeignInvoiceCommand extends AbstractImportInvoiceCommand
{
    protected $signature = 'service:ImportAutoForeignInvoice';
    protected $description = '导入外汇流水';

    private $currencyCodes = [];
    private const COMPANY_NAME = '研趣國際有限公司';

    public const FOREIGN_TASK_NAME = 'foreignInvoiceUpload';

    public function __construct()
    {
        parent::__construct();
        $this->currencyCodes = array_column(InvoiceConstants::CURRENCY, 'currencyName', 'currencyCode');
    }

    protected function getTaskName(): string
    {
        return 'foreignInvoiceUpload';
    }

    protected function getModelHeadRef(): array
    {
        return [
            '银行流水号',
            '到账日期',
            '抬头',
            '打款银行',
            '到账银行',
            '外汇金额',
            '币种',
            '入账金额',
            '收款公司账户名',
            '附言',
            '发票号',
            '备注',
        ];
    }

    protected function getModelHeadRefMap(): array
    {
        return [
            'serialno',
            'date',
            'title',
            'payment_bank',
            'arriving_bank',
            'foreign_amount',
            'currency_code',
            'amount',
            'companyname',
            'postscript',
            'invoiceno',
            'remark',
        ];
    }

    protected function getUnRequireRef(): array
    {
        return [
            'remark' => 1,
            'postscript' => 1,
            'invoiceno' => 1,
            'amount' => 1,
            'payment_bank' => 1,
        ];
    }

    protected function processRowSpecific(array $row, int $timestamp): ?array
    {
        // 处理外汇金额
        if (isset($row['foreign_amount'])) {
            $row['foreign_amount'] = $this->dealAmount($row['foreign_amount']);
        }

        // 计算外汇汇率
        if (!empty($row['foreign_amount']) && $row['foreign_amount'] > 0) {
            $row['foreign_rate'] = bcdiv($row['amount'], $row['foreign_amount'], 6);
        } else {
            $row['foreign_rate'] = 0;
        }

        if ($row['companyname'] == self::COMPANY_NAME && empty($row['amount'])) {
            $row['amount'] = 0;
        }
        $row['is_foreign_invoice'] = 1;

        return $row;
    }

    protected function validateSpecialFields(array $row): bool
    {
        // 验证外汇金额格式
        if (!empty($row['foreign_amount']) && !is_numeric($row['foreign_amount'])) {
            $this->logError('外汇金额格式不正确', $row);
            $this->errorMessages[] = [
                'message' => "外汇金额格式不正确: {$row['foreign_amount']}",
                'row_data' => $row
            ];
            return false;
        }

        // 验证币种
        if (!empty($row['currency_code']) && !isset($this->currencyCodes[$row['currency_code']])) {
            $this->logError('货币格式不正确', $row);
            $this->errorMessages[] = [
                'message' => "货币格式不正确: {$row['currency_code']}",
                'row_data' => $row
            ];
            return false;
        }

        // 验证特殊公司规则
        if ($row['companyname'] !== self::COMPANY_NAME && (empty($row['amount']) || empty($row['payment_bank']))) {
            $this->logError('收款公司账户名不为研趣國際有限公司时入账金额不能为空', $row);
            $this->errorMessages[] = [
                'message' => "收款公司账户名不为研趣國際有限公司时入账金额不能为空",
                'row_data' => $row
            ];
            return false;
        }

        if (!empty($row['amount']) && !is_numeric($row['amount'])) {
            $this->logError("金额不合法: {$row['amount']}", $row);
            $this->errorMessages[] = [
                'message' => "金额不合法: {$row['amount']}",
                'row_data' => $row
            ];
            return false;
        }

        if (!is_numeric($row['foreign_amount'])) {
            $this->logError("外汇金额不合法: {$row['foreign_amount']}", $row);
            $this->errorMessages[] = [
                'message' => "外汇金额不合法: {$row['foreign_amount']}",
                'row_data' => $row
            ];
            return false;
        }

        return true;
    }

    protected function postProcessData(Collection $data): Collection
    {
        // 外汇流水不需要地区处理
        return $data;
    }
}