<?php

namespace App\Console\Commands;

use App\Models\AccountConfig;
use App\Models\AccountInfo;
use App\Models\BuffetAddress;
use App\Models\CrmAccount;
use App\Models\JobEmail;
use App\Services\UserService;
use App\Utils\ProcessLock;
use App\Utils\QYWechatNotifier;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SendNewAccountEmailToPrincipal extends Command
{
    protected $signature = 'SendNewAccountEmailToPrincipal';
    protected $description = '发送新用户注册通知邮件给负责人';

    public function handle()
    {
        ProcessLock::doLock($this->signature);
        error_reporting(E_ALL & ~E_NOTICE);
        $fiveMinutesAgo = time() - 5 * 180;

        $accountConfigs = AccountConfig::where('accountid', '>', 450905)
            ->where('isemailtoprincipal', 0)
            ->where('adtime', '<', $fiveMinutesAgo)
            ->get();

        /*
         * 下面查绩效负责人用到了
         */
        $achievements = DB::table('crm_achievements as a')
            ->leftJoin('crm_account as b', 'a.saccountid', '=', 'b.accountid')
            ->where("a.isvoid", "0")
            ->where("b.status", 100)
            ->where("b.isnormal", 1)
            ->where("b.isvoid", 0)
            ->select("a.*")
            ->get();

        foreach ($accountConfigs as $accountConfig) {
            if ($accountConfig->accountid == 0) {
                continue;
            }

            $accountInfo = AccountInfo::where('accountid', $accountConfig->accountid)->first();
            if (!$accountInfo) {
                continue;
            }

            $emails = [];
            $tmpEmailCity = $accountInfo->city;


            if ($tmpEmailCity && intval($tmpEmailCity) > 0) {
                $tmpAddressInfo = BuffetAddress::whereRaw("concat(',',cityids,',') like ?", ['%,' . $tmpEmailCity . ',%'])->first();

                if ($tmpAddressInfo) {
                    $tmpAccountIds = explode(',', $tmpAddressInfo->accountids);
                    if (!empty($tmpAccountIds)) {
                        $tmpCrmAccounts = CrmAccount::whereIn('accountid', $tmpAccountIds)->get();
                        foreach ($tmpCrmAccounts as $crmAccount) {
                            $emails[] = $crmAccount->loginame;
                        }
                    }
                }
            }

            // 更新发送状态
            AccountConfig::where('accountid', $accountConfig->accountid)
                ->update(['isemailtoprincipal' => 1]);

            // 几个城市不发
            if (in_array($tmpAddressInfo->addressid, [4, 5, 19, 43, 49,7])) {
                continue;
            }


            // 构建邮件内容
            $emailTitle = "【科学指南针】新用户注册通知-" . $accountInfo->contacter . $accountInfo->companyname .
                "(注册时间：" . date('Y-m-d H:i:s', $accountInfo->postime) . ")";

            $emailContent = "<div style=\"font-size:16px;\">";
            $emailContent .= "姓名：" . $accountInfo->contacter . "<br />";

            // 身份判断
            $identityMap = [
                '1' => '学生',
                '2' => '教职工',
                '4' => '企业员工',
                '5' => '个人',
                '6' => '医院'
            ];

            $emailContent .= "身份：" . ($identityMap[$accountInfo->accountype] ?? '未知') . "<br />";
            $emailContent .= "所在学校/单位：" . $accountInfo->companyname . "<br />";
            $emailContent .= "课题组（部门）负责人姓名：" . $accountInfo->superiorname . "<br />";
            $emailContent .= "课题组（部门）负责人职务：" . $accountInfo->appellation . "<br />";
            $emailContent .= "课题组（部门）负责人电子邮箱：" . $accountInfo->aplemail . "<br />";
            $emailContent .= "<br /><br />";
            $emailContent .= "</div><h2>请地区品牌经理及时联系！</h2>";

            //江苏区域不发email，给品牌运营和陈慧玲发企业微信通知
            if (in_array($tmpAddressInfo->addressid, [6, 44])) {
                $userService = new UserService();
                $userAchievementsHead = $userService->getUserAchievementsHead(["accountid" => $accountConfig->accountid], $achievements);
                $userAchievementsHeadAccountId = $userAchievementsHead["data"]['saccountid'] ?? 0;

                $saleAssistants = [];
                $saleAssistants['2171'] = ['<EMAIL>', "<EMAIL>"];
                $saleAssistants['2288'] = ['<EMAIL>', "<EMAIL>"];
                $saleAssistants['2341'] = ['<EMAIL>', "<EMAIL>"];
                $saleAssistants['2418'] = ['<EMAIL>', "<EMAIL>"];
                $saleAssistants['2370'] = ['<EMAIL>', "<EMAIL>"];
                $saleAssistants['1973'] = ['<EMAIL>', "<EMAIL>"];
                $saleAssistants['2041'] = ['<EMAIL>', "<EMAIL>"];

                /*
                 * 把$emailContent中一些特殊符号去掉，改成成适合发企微消息的格式
                 */

                $emailContent = str_replace("<br />", "\n", $emailContent);
                $emailContent = str_replace("<div style=\"font-size:16px;\">", "新注册通知\n", $emailContent);
                $emailContent = str_replace("</div><h2>请地区品牌经理及时联系！</h2>", "", $emailContent);


                if (isset($saleAssistants[$userAchievementsHeadAccountId])) {
                    $notifyList = $saleAssistants[$userAchievementsHeadAccountId];
                    $notifyList[] = "<EMAIL>";

                    foreach ($notifyList as $eachEmail) {
                        QYWechatNotifier::getInstance()
                            ->setEmail($eachEmail)
                            ->notify($emailContent);
                    }
                } else {
                    QYWechatNotifier::getInstance()
                        ->setEmail("<EMAIL>")
                        ->notify($emailContent . "\n没有找到对应的负责人，只发给了你自己");

                    QYWechatNotifier::getInstance()
                        ->setEmail("<EMAIL>")
                        ->notify($emailContent . "\n没有找到对应的负责人，只发给了你自己");
                }

            } else {
                JobEmail::insert([
                    'emails' => implode(',', $emails),
                    'subject' => $emailTitle,
                    'bodys' => $emailContent,
                    'adtime' => time()
                ]);
            }

        }
    }
}
