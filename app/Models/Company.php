<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Company
 *
 * @property int $company_id 企业id
 * @property string $company_name 企业名称
 * @property int $is_standard 0非标准企业 1标准企业
 * @property int $province 省
 * @property int $city 市
 * @property int $create_time 创建时间
 * @property int $update_time 更新时间
 * @property int $is_have_same_company_name 是否拥有同样名称的标准企业 0没有 1有
 * @property int $mount_company_saccountid 取消企业挂载的saccountid
 * @property int $company_stratification 企业分层 1.A类：优质客户（业绩or潜力）、2.B类：重点客户（稳定业绩）、3.C类：普通客户、4.D类：难搞客户（防止恶劣关系）
 * @property string $main_products 主营产品
 * @property string $update_at update_time 有些没有更新 所以增加
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereCompanyStratification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereIsHaveSameCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereIsStandard($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereMainProducts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereMountCompanySaccountid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereProvince($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereUpdateAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Company whereUpdateTime($value)
 * @mixin \Eloquent
 */
class Company extends Model
{
    protected $table = "company";
    protected $primaryKey = 'company_id';
    public $timestamps = false;

    /**
     * 标准企业
     */
    const IS_STANDARD = 1;
    /**
     * 非标准企业
     */
    const NOT_STANDARD = 0;

    const PROVINCE_DEFAULT = 0;
    const CITY_DEFAULT = 0;

    /**
     * 未拥有同样名称的标准企业
     */
    const NOT_HAVE_SAME_COMPANY_NAME = 0;
    /**
     * 拥有同样名称的标准企业
     */
    const IS_HAVE_SAME_COMPANY_NAME = 1;
}
