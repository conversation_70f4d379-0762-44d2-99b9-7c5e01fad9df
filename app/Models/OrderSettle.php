<?php

namespace App\Models;

use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\OrderSettle
 *
 * @property int $settleid 自增主键
 * @property string $application_number 申请编号
 * @property int|null $unityid 统一编号,每次提交多个供应商时，该值都一样以标识是本次提交
 * @property string|null $orderids 本次结算的订单id，以逗号隔开
 * @property string|null $ordernos 本次结算的订单编号，以逗号隔开
 * @property int|null $supplierid 结算给了哪个供应商
 * @property string|null $suppliername 供应商名称
 * @property float $original_apply_amount 原始申请结算金额
 * @property float $applyamt 申请结算金额(订单定额+奖励金+变动金额+税点金额)
 * @property float $manageramt 经理确认金额
 * @property string|null $managerdesc 经理备注
 * @property float $taxamount 税点金额
 * @property float $changeamount 上次以来提交的成本变动总额
 * @property int $applyaccount 申请结算的vip帐号id
 * @property string|null $applydesc 申请时的备注
 * @property int $dealstatus 1新申请待供应商负责人确认/2供应商负责人已确认/4供应商管理部审核通过待财务打款结算/100财务已打款结算
 * @property int $invoice_status 开票状态 1未开票/2部分开票/3已开票
 * @property float $dealamt 最终结算金额
 * @property int|null $dealaccount 申请结算的admin帐号id
 * @property int|null $dealtime 最终结算时间
 * @property string|null $dealdesc admin结算时的留言记录
 * @property int $invoice 0未开票/1已开票
 * @property int $merchantsettleinfoid 结算的帐号id
 * @property string|null $acceptbankinfo 收款帐号信息(json字符串)
 * @property string|null $jsflag 结算标签
 * @property int|null $postime 申请时间
 * @property int $isconfirm 0未确认/1已确认
 * @property int $isvoid 1作废/0正常
 * @property string|null $company_id 法人实体
 * @property float $awardamount 奖励金
 * @property float $prestore 预存抵扣金额
 * @property int $submit_memberid 提交结算的成员账号 -1系统自动提交 0主账号提交 1成员账号提交
 * @property string $supplier_contacter 供应商联系人
 * @property string $ekuaibao_flow_id 易快报单据id
 * @property string $ekuaibao_flow_code 易快报单据编号
 * @property string $ekuaibao_flow_operator_crm_account_id 易快报单据的操作人crm_account_id
 * @property int $invoice_payment_order 开票付款顺序：1先款后票/2先票后款
 * @property string $specialist_auditor_crm_account_id 供应商专员审核人crm_account_id
 * @property string $apply_amount_change_reason_types 修改申请金额的原因类型
 * @property string $apply_amount_change_reason_comment 修改申请金额的原因说明
 * @property ProviderMerchant|null $providerMerchant
 *
 * @method static Builder|OrderSettle newModelQuery()
 * @method static Builder|OrderSettle newQuery()
 * @method static Builder|OrderSettle query()
 * @method static Builder|OrderSettle whereSettleid($value)
 * @method static Builder|OrderSettle whereApplicationNumber($value)
 * @method static Builder|OrderSettle whereUnityid($value)
 * @method static Builder|OrderSettle whereOrderids($value)
 * @method static Builder|OrderSettle whereOrdernos($value)
 * @method static Builder|OrderSettle whereSupplierid($value)
 * @method static Builder|OrderSettle whereSuppliername($value)
 * @method static Builder|OrderSettle whereOriginalApplyAmount($value)
 * @method static Builder|OrderSettle whereApplyamt($value)
 * @method static Builder|OrderSettle whereManageramt($value)
 * @method static Builder|OrderSettle whereManagerdesc($value)
 * @method static Builder|OrderSettle whereTaxamount($value)
 * @method static Builder|OrderSettle whereChangeamount($value)
 * @method static Builder|OrderSettle whereApplyaccount($value)
 * @method static Builder|OrderSettle whereApplydesc($value)
 * @method static Builder|OrderSettle whereDealstatus($value)
 * @method static Builder|OrderSettle whereInvoiceStatus($value)
 * @method static Builder|OrderSettle whereDealamt($value)
 * @method static Builder|OrderSettle whereDealaccount($value)
 * @method static Builder|OrderSettle whereDealtime($value)
 * @method static Builder|OrderSettle whereDealdesc($value)
 * @method static Builder|OrderSettle whereInvoice($value)
 * @method static Builder|OrderSettle whereMerchantsettleinfoid($value)
 * @method static Builder|OrderSettle whereAcceptbankinfo($value)
 * @method static Builder|OrderSettle whereJsflag($value)
 * @method static Builder|OrderSettle wherePostime($value)
 * @method static Builder|OrderSettle whereIsconfirm($value)
 * @method static Builder|OrderSettle whereIsvoid($value)
 * @method static Builder|OrderSettle whereCompanyId($value)
 * @method static Builder|OrderSettle whereAwardamount($value)
 * @method static Builder|OrderSettle wherePrestore($value)
 * @method static Builder|OrderSettle whereSubmitMemberid($value)
 * @method static Builder|OrderSettle whereSupplierContacter($value)
 * @method static Builder|OrderSettle whereEkuaibaoFlowId($value)
 * @method static Builder|OrderSettle whereEkuaibaoFlowCode($value)
 * @method static Builder|OrderSettle whereEkuaibaoFlowOperatorCrmAccountId($value)
 * @method static Builder|OrderSettle whereInvoicePaymentOrder($value)
 * @method static Builder|OrderSettle whereSpecialistAuditorCrmAccountId($value)
 * @method static Builder|OrderSettle whereApplyAmountChangeReasonTypes($value)
 * @method static Builder|OrderSettle whereApplyAmountChangeReasonComment($value)
 *
 * @mixin \Eloquent
 */
class OrderSettle extends Model
{
    protected $table = 'order_settle';
    public $timestamps = false;

    public function settleChanges()
    {
        return $this->hasMany(OrderSettleChange::class, 'settleid', 'settleid');
    }

    public function providerMerchant()
    {
        return $this->hasOne(ProviderMerchant::class, 'providermerchantid', 'supplierid');
    }

    public function invoiceToPaymentRelations()
    {
        return $this
            ->hasMany(ProviderMerchantInvoiceToPaymentRelation::class, 'related_application_id', 'settleid')
            ->where('relation_type', ProviderMerchantInvoiceConstants::RELATE_TO_PAYMENT_TYPE_SETTLE);
    }

    public function invoiceReconciliationToPaymentRelations()
    {
        return $this
            ->hasMany(ProviderMerchantInvoiceReconciliationToPaymentRelation::class,
                'related_application_id', 'settleid')
            ->where('relation_type', ProviderMerchantInvoiceReconciliationConstants::RECONCILE_TYPE_PREPAYMENT);
    }
}