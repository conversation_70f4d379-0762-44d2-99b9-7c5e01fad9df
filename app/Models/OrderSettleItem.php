<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\OrderSettleItem
 *
 * @property int $itemid 自增主键
 * @property int|null $settleid 属于哪一次结算
 * @property int|null $orderid 订单id
 * @property string|null $orderno 订单编号
 * @property float $settleamt 该订单最终结算金额
 * @property int $origin_cost 本结算明细的原始成本（settleamt折扣前的金额）
 * @property int $dealstatus 结算处理状态
 * @property int|null $dealtime 最终结算时间
 * @property int|null $postime 申请时间
 * @property int $isvoid 作废标识
 * @property float $sharetaxmoney 承担税金
 * @property float $detaxmoney 剥离税金
 * @property float $share_apply_delta 分摊的申请金额差值
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereItemid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereSettleid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereOrderid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereOrderno($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereSettleamt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereOriginCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereDealstatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereDealtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem wherePostime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereIsvoid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereSharetaxmoney($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereDetaxmoney($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderSettleItem whereShareApplyDelta($value)
 * @mixin \Eloquent
 */
class OrderSettleItem extends Model
{
    protected $table = 'order_settleitem';
    protected $primaryKey = 'itemid';
    public $timestamps = false;
}