<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\TitleUnivsRef
 *
 * @property int $refid 映射id
 * @property string|null $title 银行流水抬头
 * @property string|null $invoicetitle 发票抬头
 * @property string|null $createtime 创建映射的时间
 * @property float|null $weight 权重
 * @property int|null $add_source 1流水手动置为已处理时自动添加
 * @property string $proof_url 映射证明
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TitleUnivsRef newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TitleUnivsRef newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TitleUnivsRef query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TitleUnivsRef whereAddSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TitleUnivsRef whereCreatetime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TitleUnivsRef whereInvoicetitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TitleUnivsRef whereProofUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TitleUnivsRef whereRefid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TitleUnivsRef whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\TitleUnivsRef whereWeight($value)
 * @mixin \Eloquent
 */
class TitleUnivsRef extends Model
{
    protected $primaryKey = 'refid';
    public $timestamps = false;

    protected $table = 'title_univs_ref';
}
