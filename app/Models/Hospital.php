<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Hospital
 *
 * @property int $hospitalid 医院id
 * @property string|null $hname 名称
 * @property int|null $province
 * @property int|null $city
 * @property int|null $area
 * @property string|null $address
 * @property string|null $fax 传真
 * @property string|null $phone 电话
 * @property string|null $chief 负责人
 * @property string|null $grade 级别
 * @property int|null $adtime 添加时间
 * @property int|null $sort 排序字段
 * @property string|null $expert 擅长科室
 * @property string|null $email 联系邮箱
 * @property string|null $website 网站
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereAdtime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereArea($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereChief($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereExpert($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereFax($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereGrade($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereHname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereHospitalid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereProvince($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\Hospital whereWebsite($value)
 * @mixin \Eloquent
 */
class Hospital extends Model
{
    protected $table = "hospital";
    protected $primaryKey = 'hospitalid';
    public $timestamps = false;
}
