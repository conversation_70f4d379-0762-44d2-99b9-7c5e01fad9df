<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\AutoInvoice
 *
 * @property int $serialno 银行流水账号
 * @property int|null $date 到账日期
 * @property string|null $title 发票抬头
 * @property float|null $amount 金额
 * @property float $unmatchamount 未匹配金额-20210802-林茂
 * @property string|null $companyname 收款公司账户名称
 * @property string|null $postscript 附言
 * @property string|null $invoiceno 发票号（多个用逗号隔开）
 * @property int|null $state 状态 0待处理 1已处理（自动） 2已处理（手动）
 * @property string|null $remark 备注
 * @property int $ischeck 是否确认匹配结果 0未确认 1已确认-*********林茂
 * @property int|null $create 创建时间
 * @property int|null $update 更新时间
 * @property int $matched 0未匹配 1已匹配
 * @property string|null $follower 推送以后的跟进人
 * @property int $is_clean 1:已清洗
 * @property int $exception_id exception表id
 * @property int $source 0：导入，1:22.11.17手动导入
 * @property int $match_combine_count 匹配组合数
 * @property string $payment_bank 打款银行
 * @property string $currency_code 币种
 * @property float $foreign_exchange_amount 外汇转换的入账人民币金额 只有外汇流水时才会有值
 * @property int $is_foreign_invoice 是否外汇流水 1是 0不是
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereCompanyname($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereCreate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereCurrencyCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereExceptionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereFollower($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereForeignExchangeAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereInvoiceno($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereIsClean($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereIsForeignInvoice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereIscheck($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereMatchCombineCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereMatched($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice wherePaymentBank($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice wherePostscript($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereSerialno($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereUnmatchamount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereUpdate($value)
 * @mixin \Eloquent
 * @property int $city_id 城市id
 * @property int $province_id 省id
 * @property float $foreign_amount 外汇金额 只有外汇流水时才会有值
 * @property float $foreign_rate 外币汇率
 * @property string $arriving_bank 到账银行
 * @property int $check_admin_id 操作匹配的admin账号id
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereArrivingBank($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereCheckAdminId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereCityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereForeignAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereForeignRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Models\AutoInvoice whereProvinceId($value)
 */
class AutoInvoice extends Model
{
    public $timestamps = false;

    protected $table = 'auto_invoice';

    protected $primaryKey = 'serialno';

    protected $guarded = [];
}
