<?php

namespace App\Models;

use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;


/**
 * App\Models\ProviderMerchantInvoiceReconciliationApplication
 *
 * @property int $id 主键
 * @property int $reconciliation_type 核销类型：1供应商预存申请/2供应商结算申请
 * @property float $reconciliation_amount 核销金额
 * @property string $ekuaibao_company_code 签约主体：ekuaibao_company.code
 * @property int $process_status 状态：1待审核\2已驳回\3已完成
 * @property int $settle_info_id 结算账号id：provider_merchantsettleinfo.merchantsettleinfoid
 * @property string|null $settle_infos_json 提交时的结算账号信息json
 * @property string $provider_merchant_subject_name 供应商主体
 * @property string $ekuaibao_flow_code 易快报单据编号
 * @property int $ekuaibao_flow_operator_crm_account_id 易快报单据的操作人crm_account_id
 * @property int $apply_crm_account_id 申请人crm_account_id：crm_account.accountid
 * @property int $specialist_auditor_crm_account_id 供应商专员审核人crm_account_id
 * @property string $audit_comment_by_specialist 供应商专员审核核销申请的说明
 * @property \Illuminate\Support\Carbon $create_time 创建时间
 * @property \Illuminate\Support\Carbon $update_time 更新时间
 * @property int $isvoid 是否作废
 *
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication newModelQuery()
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication newQuery()
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication query()
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereId($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereReconciliationType($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereReconciliationAmount($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereEkuaibaoCompanyCode($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereProcessStatus($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereSettleInfoId($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereSettleInfosJson($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereProviderMerchantSubjectName($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereEkuaibaoFlowCode($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereEkuaibaoFlowOperatorCrmAccountId($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereApplyCrmAccountId($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereAuditCommentBySpecialist($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereCreateTime($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereUpdateTime($value)
 * @method static Builder|ProviderMerchantInvoiceReconciliationApplication whereIsvoid($value)
 *
 * @mixin \Eloquent
 */
class ProviderMerchantInvoiceReconciliationApplication extends Model
{
    protected $table = 'provider_merchant_invoice_reconciliation_application';
    public $timestamps = false;

    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';

    public function providerMerchant()
    {
        return $this->belongsTo(ProviderMerchant::class, 'providermerchantid', 'provider_merchant_id');
    }

    public function providerPrestores()
    {
        return $this->belongsToMany(ProviderPrestore::class,
            'provider_merchant_invoice_reconciliation_to_payment_relation',
            'reconciliation_id', 'related_application_id')
            ->wherePivot('isvoid', 0)
            ->wherePivot('relation_type',
                ProviderMerchantInvoiceReconciliationConstants::RELATE_TO_PAYMENT_TYPE_PREPAYMENT);
    }
}