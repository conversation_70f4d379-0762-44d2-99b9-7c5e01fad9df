<?php

namespace App\Constants\Ekuaibao\CustomDimension;

use App\Utils\EnvironmentHelper;

/**
 * 自定义档案常量
 */
class EkuaibaoCustomDimensionConstants
{
    const PRODUCTION_DEPARTMENT_ATTRIBUTE_DIMENSION_ID = "EDAGUrmQNW5Diw:部门属性";
    const TEST_DEPARTMENT_ATTRIBUTE_DIMENSION_ID = "RUIcN9Sh1VsE00:部门属性";
    const PRODUCTION_PAYMENT_TYPE_DIMENSION_ID = "EDAGUrmQNW5Diw:付款类型";
    const TEST_PAYMENT_TYPE_DIMENSION_ID = "RUIcN9Sh1VsE00:付款类型";
    const PRODUCTION_ARCHIVE_FLAG_DIMENSION_ID = "“EDAGUrmQNW5Diw:归档标记";
    const TEST_ARCHIVE_FLAG_DIMENSION_ID = "RUIcN9Sh1VsE00:归档标记";
    const PRODUCTION_YES_OR_NO_DIMENSION_ID = "EDAGUrmQNW5Diw:是否";
    const TEST_YES_OR_NO_DIMENSION_ID = "RUIcN9Sh1VsE00:是与否";
    /*
     * 付款类型的“供应商预存”属性项名称
     */
    const PAYMENT_TYPE_ITEM_NAME_SUPPLIER_PREPAYMENT = "供应商预存";
    /**
     * 付款类型的“供应商结算”属性项名称
     */
    const PAYMENT_TYPE_ITEM_NAME_SUPPLIER_SETTLEMENT = "供应商结算";
    /*
     * 是否的“否”属性项名称
     */
    const YES_OR_NO_ITEM_NAME_NO = "否";
    /**
     * 归档标记的“已归档”属性项名称
     */
    const ARCHIVE_FLAG_ITEM_NAME_ARCHIVED = "已归档";

}