<?php

namespace App\Constants\Ekuaibao;

class EkuaibaoPropertyConstants
{
    /**
     * 生产环境的“法人实体”字段名称
     */
    const PRODUCTION_LEGAL_ENTITY_NAME = '法人实体';
    /**
     * 测试环境的“法人实体”字段名称
     */
    const TEST_LEGAL_ENTITY_NAME = '法人实体';
    /**
     * 生产环境的“提交人部门”字段名称
     */
    const PRODUCTION_SUBMITTER_DEPARTMENT_NAME = 'u_提交人部门';
    /**
     * 测试环境的“提交人部门”字段名称
     */
    const TEST_SUBMITTER_DEPARTMENT_NAME = 'u_提交人部门';
    /**
     * 生产环境的“部门属性”字段名称
     */
    const PRODUCTION_DEPARTMENT_ATTRIBUTE_NAME = 'u_部门属性';
    /**
     * 测试环境的“部门属性”字段名称
     */
    const TEST_DEPARTMENT_ATTRIBUTE_NAME = 'u_K1tP_部门属性';
    /**
     * 生产环境的“付款类型”字段名称
     */
    const PRODUCTION_PAYMENT_TYPE_NAME = 'u_付款类型1';
    /**
     * 测试环境的“付款类型”字段名称
     */
    const TEST_PAYMENT_TYPE_NAME = 'u_ofdz_付款类型1';
    /**
     * 生产环境的“是否涉及尾款支付”字段名称
     */
    const PRODUCTION_IS_FINAL_PAYMENT_NAME = 'u_是否涉及尾款支付';
    /**
     * 测试环境的“是否涉及尾款支付”字段名称
     */
    const TEST_IS_FINAL_PAYMENT_NAME = 'u_hZih_是否涉及尾款支付';
    /**
     * 生产环境的“参与人”字段名称
     */
    const PRODUCTION_PARTICIPANT_NAME = 'u_参与人';
    /**
     * 测试环境的“参与人”字段名称
     */
    const TEST_PARTICIPANT_NAME = 'u_参与人多选';
    /**
     * 生产环境的“归档标记”字段名称
     */
    const PRODUCTION_ARCHIVE_FLAG_NAME = 'u_归档标记';
    /**
     * 测试环境的“归档标记”字段名称
     */
    const TEST_ARCHIVE_FLAG_NAME = 'u_归档标记';
    /**
     * 生产环境的“回调标记”字段名称
     */
    const PRODUCTION_CALLBACK_FLAG_NAME = 'u_回调标记';
    /**
     * 测试环境的“回调标记”字段名称
     */
    const TEST_CALLBACK_FLAG_NAME = 'u_回调标记';
    /**
     * 生产环境的“预计到票日期”字段名称
     */
    const PRODUCTION_EXPECTED_INVOICE_DATE_NAME = 'u_预计到票日期';
    /**
     * 测试环境的“预计到票日期”字段名称
     */
    const TEST_EXPECTED_INVOICE_DATE_NAME = 'u_RNkR_预计到票日期';
    //$contractQuantity
    /**
     * 生产环境的“合同数量”字段名称
     */
    const PRODUCTION_CONTRACT_QUANTITY_NAME = 'u_合同数量';
    /**
     * 测试环境的“合同数量”字段名称
     */
    const TEST_CONTRACT_QUANTITY_NAME = 'u_合同数量';
}