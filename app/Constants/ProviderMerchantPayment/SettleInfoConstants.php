<?php

namespace App\Constants\ProviderMerchantPayment;

class SettleInfoConstants
{
    /**
     * 付款方式-对私
     */
    const PAYMENT_MODE_PERSONAL = 1;
    /**
     * 付款方式-对公
     */
    const PAYMENT_MODE_CORPORATE = 2;
    const PAYMENT_MODE_NAME_MAP = [
        self::PAYMENT_MODE_PERSONAL => '对私',
        self::PAYMENT_MODE_CORPORATE => '对公',
    ];

    /**
     * 付款方式-支付宝
     */
    const PAYMENT_METHOD_ALIPAY = 1;
    /**
     * 付款方式-微信
     */
    const PAYMENT_METHOD_WECHAT = 2;
    /**
     * 付款方式-银行卡
     */
    const PAYMENT_METHOD_BANK_CARD = 3;
    /**
     * 开票付款顺序-先款后票
     */
    const INVOICE_PAYMENT_ORDER_PAYMENT_FIRST = 1;
    /**
     * 开票付款顺序-先票后款
     */
    const INVOICE_PAYMENT_ORDER_INVOICE_FIRST = 2;
}