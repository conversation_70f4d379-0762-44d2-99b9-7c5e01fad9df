<?php

namespace App\Constants\ProviderMerchantPayment;

class ProviderMerchantInvoiceReconciliationConstants
{
    /**
     * 状态-待财务审核
     */
    const PROCESS_STATUS_TO_BE_REVIEWED = 1;
    /**
     * 状态-已驳回
     */
    const PROCESS_STATUS_REJECTED = 2;
    /**
     * 状态-已完成
     */
    const PROCESS_STATUS_COMPLETED = 3;
    /**
     * 状态-待专员审核
     */
    const PROCESS_STATUS_TO_BE_SPECIALIST_AUDITED = 4;

    const PROCESS_STATUS_MAP = [
        self::PROCESS_STATUS_TO_BE_REVIEWED => '待财务审核',
        self::PROCESS_STATUS_REJECTED => '已驳回',
        self::PROCESS_STATUS_COMPLETED => '已通过',
        self::PROCESS_STATUS_TO_BE_SPECIALIST_AUDITED => '待专员审核',
    ];
    const PROCESS_STATUS_ENUMS = [
        ['value' => self::PROCESS_STATUS_TO_BE_REVIEWED, 'label' => '待财务审核'],
        ['value' => self::PROCESS_STATUS_REJECTED, 'label' => '已驳回'],
        ['value' => self::PROCESS_STATUS_COMPLETED, 'label' => '已通过'],
        ['value' => self::PROCESS_STATUS_TO_BE_SPECIALIST_AUDITED, 'label' => '待专员审核'],
    ];

    /**
     * 核销类型-供应商预存申请
     */
    const RECONCILE_TYPE_PREPAYMENT = 1;
    /**
     * 核销类型-供应商结算申请
     */
    const RECONCILE_TYPE_SETTLEMENT = 2;
    const RECONCILE_TYPE_MAP = [
        self::RECONCILE_TYPE_PREPAYMENT => '预存',
        self::RECONCILE_TYPE_SETTLEMENT => '订单结算'
    ];
    const RECONCILE_TYPE_ENUMS = [
        ['value' => self::RECONCILE_TYPE_PREPAYMENT, 'label' => '预存'],
        ['value' => self::RECONCILE_TYPE_SETTLEMENT, 'label' => '订单结算']
    ];

    /**
     * 关联打款类型-预存
     */
    const RELATE_TO_PAYMENT_TYPE_PREPAYMENT = 1;
    /**
     * 关联打款类型-结算
     */
    const RELATE_TO_PAYMENT_TYPE_SETTLEMENT = 2;
    /**
     * 易快报单据标题
     */
    const EKUAIBAO_FLOW_TITLE = '供应商%s核销%s-%s';
    /**
     * 易快报单据描述
     */
    const EKUAIBAO_FLOW_DESCRIPTION = '供应商核销';
    /**
     * 审核结果-通过
     */
    const AUDIT_RESULT_PASS = 1;
    /**
     * 审核结果-驳回
     */
    const AUDIT_RESULT_REJECT = 2;
    const AUDIT_RESULT_MAP = [
        self::AUDIT_RESULT_PASS => '通过',
        self::AUDIT_RESULT_REJECT => '驳回'
    ];

    /**
     * 枚举场景-admin
     */
    const ENUM_SCENE_ADMIN = 'admin';
}