<?php

namespace App\Entities\InvoiceTool;

class InvoiceOcrDTO
{
    private $fileUrl;
    private $ocrResult;
    private $invoiceType;
    private $invoiceNumber;
    private $invoiceCode;
    private $invoiceTime;
    private $buyerName;
    private $purchaserBank;
    private $sellerName;
    private $sellerBank;
    private $taxInclusiveAmount;
    private $taxExclusiveAmount;
    private $invoiceValidationCode;
    private $taxRate;
    private $buyerTaxNumber;

    public function __construct($data)
    {
        $this->fileUrl = $data['file_url'] ?? '';
        $this->ocrResult = $data['ocr_result'] ?? '';
        $this->invoiceType = $data['invoice_type'] ?? 0;
        $this->invoiceNumber = $data['invoice_number'] ?? '';
        $this->invoiceCode = $data['invoice_code'] ?? '';
        $this->invoiceTime = $data['invoice_time'] ?? '';
        $this->buyerName = $data['buyer_name'] ?? '';
        $this->purchaserBank = $data['purchaser_bank'] ?? '';
        $this->sellerName = $data['seller_name'] ?? '';
        $this->sellerBank = $data['seller_bank'] ?? '';
        $this->taxInclusiveAmount = $data['tax_inclusive_amount'] ?? '0.00';
        $this->taxExclusiveAmount = $data['tax_exclusive_amount'] ?? '0.00';
        $this->invoiceValidationCode = $data['invoice_validation_code'] ?? '';
        $this->taxRate = $data['tax_rate'] ?? '';
        $this->buyerTaxNumber = $data['buyer_tax_number'] ?? '';
    }

    /**
     * @return mixed
     */
    public function getFileUrl()
    {
        return $this->fileUrl;
    }

    /**
     * @param mixed $fileUrl
     */
    public function setFileUrl($fileUrl): void
    {
        $this->fileUrl = $fileUrl;
    }

    /**
     * @return mixed
     */
    public function getOcrResult()
    {
        return $this->ocrResult;
    }

    /**
     * @param mixed $ocrResult
     */
    public function setOcrResult($ocrResult): void
    {
        $this->ocrResult = $ocrResult;
    }

    /**
     * @return mixed
     */
    public function getInvoiceType()
    {
        return $this->invoiceType;
    }

    /**
     * @param mixed $invoiceType
     */
    public function setInvoiceType($invoiceType): void
    {
        $this->invoiceType = $invoiceType;
    }

    /**
     * @return mixed
     */
    public function getInvoiceNumber()
    {
        return $this->invoiceNumber;
    }

    /**
     * @param mixed $invoiceNumber
     */
    public function setInvoiceNumber($invoiceNumber): void
    {
        $this->invoiceNumber = $invoiceNumber;
    }

    /**
     * @return mixed
     */
    public function getInvoiceCode()
    {
        return $this->invoiceCode;
    }

    /**
     * @param mixed $invoiceCode
     */
    public function setInvoiceCode($invoiceCode): void
    {
        $this->invoiceCode = $invoiceCode;
    }

    /**
     * @return mixed
     */
    public function getInvoiceTime()
    {
        return $this->invoiceTime;
    }

    /**
     * @param mixed $invoiceTime
     */
    public function setInvoiceTime($invoiceTime): void
    {
        $this->invoiceTime = $invoiceTime;
    }

    /**
     * @return mixed
     */
    public function getBuyerName()
    {
        return $this->buyerName;
    }

    /**
     * @param mixed $buyerName
     */
    public function setBuyerName($buyerName): void
    {
        $this->buyerName = $buyerName;
    }

    /**
     * @return mixed
     */
    public function getPurchaserBank()
    {
        return $this->purchaserBank;
    }

    /**
     * @param mixed $purchaserBank
     */
    public function setPurchaserBank($purchaserBank): void
    {
        $this->purchaserBank = $purchaserBank;
    }

    /**
     * @return mixed
     */
    public function getSellerName()
    {
        return $this->sellerName;
    }

    /**
     * @param mixed $sellerName
     */
    public function setSellerName($sellerName): void
    {
        $this->sellerName = $sellerName;
    }

    /**
     * @return mixed
     */
    public function getSellerBank()
    {
        return $this->sellerBank;
    }

    /**
     * @param mixed $sellerBank
     */
    public function setSellerBank($sellerBank): void
    {
        $this->sellerBank = $sellerBank;
    }

    /**
     * @return mixed
     */
    public function getTaxInclusiveAmount()
    {
        return $this->taxInclusiveAmount;
    }

    /**
     * @param mixed $taxInclusiveAmount
     */
    public function setTaxInclusiveAmount($taxInclusiveAmount): void
    {
        $this->taxInclusiveAmount = $taxInclusiveAmount;
    }

    /**
     * @return mixed
     */
    public function getTaxExclusiveAmount()
    {
        return $this->taxExclusiveAmount;
    }

    /**
     * @param mixed $taxExclusiveAmount
     */
    public function setTaxExclusiveAmount($taxExclusiveAmount): void
    {
        $this->taxExclusiveAmount = $taxExclusiveAmount;
    }

    /**
     * @return mixed
     */
    public function getInvoiceValidationCode()
    {
        return $this->invoiceValidationCode;
    }

    /**
     * @param mixed $invoiceValidationCode
     */
    public function setInvoiceValidationCode($invoiceValidationCode): void
    {
        $this->invoiceValidationCode = $invoiceValidationCode;
    }

    /**
     * @return mixed
     */
    public function getTaxRate()
    {
        return $this->taxRate;
    }

    /**
     * @param mixed $taxRate
     */
    public function setTaxRate($taxRate): void
    {
        $this->taxRate = $taxRate;
    }

    /**
     * @return mixed|string
     */
    public function getBuyerTaxNumber()
    {
        return $this->buyerTaxNumber;
    }

    /**
     * @param mixed|string $buyerTaxNumber
     */
    public function setBuyerTaxNumber($buyerTaxNumber): void
    {
        $this->buyerTaxNumber = $buyerTaxNumber;
    }
}