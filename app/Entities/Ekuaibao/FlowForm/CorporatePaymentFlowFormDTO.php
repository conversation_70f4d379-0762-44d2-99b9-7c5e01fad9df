<?php

namespace App\Entities\Ekuaibao\FlowForm;

/**
 * 对公付款单（有发票）单据详情
 */
class CorporatePaymentFlowFormDTO extends BaseFlowFormDTO
{
    /**
     * @var string 提交人部门
     */
    private $submitterDepartment;

    /**
     * @var string 部门属性
     */
    private $departmentAttribute;

    /**
     * @var string 付款类型
     */
    private $paymentType;

    /**
     * @var string 备注
     */
    private $remark;

    /**
     * @var string 是否为房屋租赁相关费用支付
     */
    private $isHouseRental;

    /**
     * @var string 是否为两千元以下的无合同支付
     */
    private $isSmallNoContract;

    /**
     * @var string 是否属于尾款支付
     */
    private $isFinalPayment;

    /**
     * @var string 合同档案
     */
    private $contractArchive;

    /**
     * @var string 对方盖章附件
     */
    private $stampedAttachment;

    /**
     * @var string 参与人
     */
    private $participants;

    /**
     * @var string 合同编号
     */
    private $contractNumber;

    /**
     * @var string 合同类型
     */
    private $contractType;

    /**
     * @var string 对方单位名称
     */
    private $otherCompanyName;

    /**
     * @var int|null 合同数量
     */
    private $contractQuantity;

    /**
     * @var float 合同总金额
     */
    private $contractTotalAmount;

    /**
     * @var string 发票的类型
     */
    private $invoiceType;

    /**
     * @var string 合同结算条款
     */
    private $settlementTerms;

    /**
     * @var string 是否涉及固定资产验收报告
     */
    private $isFixedAssetAcceptance;

    /**
     * @var string 归档标记
     */
    private $archiveFlag;

    /**
     * @var string 回调标记
     */
    private $callbackFlag;

    /**
     * @var string 标记
     */
    private $flag;

    public function getSubmitterDepartment(): string
    {
        return $this->submitterDepartment;
    }

    public function setSubmitterDepartment(string $submitterDepartment): void
    {
        $this->submitterDepartment = $submitterDepartment;
    }

    public function getDepartmentAttribute(): string
    {
        return $this->departmentAttribute;
    }

    public function setDepartmentAttribute(string $departmentAttribute): void
    {
        $this->departmentAttribute = $departmentAttribute;
    }

    public function getPaymentType(): string
    {
        return $this->paymentType;
    }

    public function setPaymentType(string $paymentType): void
    {
        $this->paymentType = $paymentType;
    }

    public function getRemark(): string
    {
        return $this->remark;
    }

    public function setRemark(string $remark): void
    {
        $this->remark = $remark;
    }

    public function getIsHouseRental(): string
    {
        return $this->isHouseRental;
    }

    public function setIsHouseRental(string $isHouseRental): void
    {
        $this->isHouseRental = $isHouseRental;
    }

    public function getIsSmallNoContract(): string
    {
        return $this->isSmallNoContract;
    }

    public function setIsSmallNoContract(string $isSmallNoContract): void
    {
        $this->isSmallNoContract = $isSmallNoContract;
    }

    public function getIsFinalPayment(): string
    {
        return $this->isFinalPayment;
    }

    public function setIsFinalPayment(string $isFinalPayment): void
    {
        $this->isFinalPayment = $isFinalPayment;
    }

    public function getContractArchive(): string
    {
        return $this->contractArchive;
    }

    public function setContractArchive(string $contractArchive): void
    {
        $this->contractArchive = $contractArchive;
    }

    public function getStampedAttachment(): string
    {
        return $this->stampedAttachment;
    }

    public function setStampedAttachment(string $stampedAttachment): void
    {
        $this->stampedAttachment = $stampedAttachment;
    }

    public function getParticipants(): string
    {
        return $this->participants;
    }

    public function setParticipants(string $participants): void
    {
        $this->participants = $participants;
    }

    public function getContractNumber(): string
    {
        return $this->contractNumber;
    }

    public function setContractNumber(string $contractNumber): void
    {
        $this->contractNumber = $contractNumber;
    }

    public function getContractType(): string
    {
        return $this->contractType;
    }

    public function setContractType(string $contractType): void
    {
        $this->contractType = $contractType;
    }

    public function getOtherCompanyName(): string
    {
        return $this->otherCompanyName;
    }

    public function setOtherCompanyName(string $otherCompanyName): void
    {
        $this->otherCompanyName = $otherCompanyName;
    }

    public function getContractQuantity(): ?int
    {
        return $this->contractQuantity;
    }

    public function setContractQuantity(int $contractQuantity): void
    {
        $this->contractQuantity = $contractQuantity;
    }

    public function getContractTotalAmount(): float
    {
        return $this->contractTotalAmount;
    }

    public function setContractTotalAmount(float $contractTotalAmount): void
    {
        $this->contractTotalAmount = $contractTotalAmount;
    }

    public function getInvoiceType(): string
    {
        return $this->invoiceType;
    }

    public function setInvoiceType(string $invoiceType): void
    {
        $this->invoiceType = $invoiceType;
    }

    public function getSettlementTerms(): string
    {
        return $this->settlementTerms;
    }

    public function setSettlementTerms(string $settlementTerms): void
    {
        $this->settlementTerms = $settlementTerms;
    }

    public function getIsFixedAssetAcceptance(): string
    {
        return $this->isFixedAssetAcceptance;
    }

    public function setIsFixedAssetAcceptance(string $isFixedAssetAcceptance): void
    {
        $this->isFixedAssetAcceptance = $isFixedAssetAcceptance;
    }

    public function getArchiveFlag(): string
    {
        return $this->archiveFlag;
    }

    public function setArchiveFlag(string $archiveFlag): void
    {
        $this->archiveFlag = $archiveFlag;
    }

    public function getCallbackFlag(): string
    {
        return $this->callbackFlag;
    }

    public function setCallbackFlag(string $callbackFlag): void
    {
        $this->callbackFlag = $callbackFlag;
    }

    public function getFlag(): string
    {
        return $this->flag;
    }

    public function setFlag(string $flag): void
    {
        $this->flag = $flag;
    }
}