<?php

namespace App\Entities\Ekuaibao\Staff;

class EkuaibaoStaffDTO
{
    /**
     * @var string 员工ID
     */
    private $id;
    /**
     * @var string 员工姓名
     */
    private $name;
    /**
     * @var string 员工别名
     */
    private $nickName;
    /**
     * @var string 员工工号
     */
    private $code;
    /**
     * @var array 所属部门ID集合
     */
    private $departments;
    /**
     * @var string 默认部门ID
     */
    private $defaultDepartment;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getNickName(): ?string
    {
        return $this->nickName;
    }

    public function setNickName(string $nickName): void
    {
        $this->nickName = $nickName;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): void
    {
        $this->code = $code;
    }

    public function getDepartments(): ?array
    {
        return $this->departments;
    }

    public function setDepartments(array $departments): void
    {
        $this->departments = $departments;
    }

    public function getDefaultDepartment(): ?string
    {
        return $this->defaultDepartment;
    }

    public function setDefaultDepartment(string $defaultDepartment): void
    {
        $this->defaultDepartment = $defaultDepartment;
    }
}