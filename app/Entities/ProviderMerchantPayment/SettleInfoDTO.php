<?php

namespace App\Entities\ProviderMerchantPayment;

class SettleInfoDTO
{
    /**
     * @var int|null 结算信息ID
     */
    private $settleInfoId;

    /**
     * @var int|null 供应商ID
     */
    private $providerMerchantId;

    /**
     * @var int|null 对公/对私
     */
    private $paymentMode;

    /**
     * @var int|null 支付方式
     */
    private $paymentMethod;

    /**
     * @var string|null 支付宝账号
     */
    private $alipayAccount;

    /**
     * @var string|null 支付宝姓名
     */
    private $alipayName;

    /**
     * @var string|null 微信账号
     */
    private $wechatPay;

    /**
     * @var string|null 开卡银行
     */
    private $openingBank;

    /**
     * @var string|null 银行账号
     */
    private $bankAccount;

    /**
     * @var string|null 银行名称
     */
    private $bankName;

    /**
     * @var string|null 银行行号
     */
    private $bankCode;

    /**
     * @var int|null 先转账后开发票/先开发票后转账
     */
    private $invoicePaymentOrder;

    /**
     * @var float|null 税点系数
     */
    private $tax;

    /**
     * @var string|null 身份证
     */
    private $idCard;

    /**
     * @var int|null 是否作废
     */
    private $isVoid;

    /**
     * @var string|null 票面税点
     */
    private $invoiceTax;

    /**
     * @var string|null 签约主体公司
     */
    private $companyId;

    /**
     * @var int|null 付款账期
     */
    private $paymentPeriod;

    /**
     * @var int|null 发票类型
     */
    private $ticketType;

    /**
     * @var string|null 收款手机号
     */
    private $payeeMobile;

    /**
     * SettleInfoDTO constructor.
     *
     * @param array $data 数据数组
     */
    public function __construct(array $data = [])
    {
        $this->settleInfoId = $data['merchantSettleInfoId'] ?? null;
        $this->providerMerchantId = $data['providerMerchantId'] ?? null;
        $this->paymentMode = $data['paymentMode'] ?? null;
        $this->paymentMethod = $data['paymentMethod'] ?? null;
        $this->alipayAccount = $data['alipayAccount'] ?? null;
        $this->alipayName = $data['alipayName'] ?? null;
        $this->wechatPay = $data['wechatPay'] ?? null;
        $this->openingBank = $data['openingBank'] ?? null;
        $this->bankAccount = $data['bankAccount'] ?? null;
        $this->bankName = $data['bankName'] ?? null;
        $this->bankCode = $data['bankCode'] ?? null;
        $this->invoicePaymentOrder = $data['invoicePaymentOrder'] ?? null;
        $this->tax = $data['tax'] ?? null;
        $this->idCard = $data['idCard'] ?? null;
        $this->isVoid = $data['isVoid'] ?? null;
        $this->invoiceTax = $data['invoiceTax'] ?? null;
        $this->companyId = $data['companyId'] ?? null;
        $this->paymentPeriod = $data['paymentPeriod'] ?? null;
        $this->ticketType = $data['ticketType'] ?? null;
        $this->payeeMobile = $data['payeeMobile'] ?? null;
    }

    /**
     * @return int|mixed|null
     */
    public function getSettleInfoId()
    {
        return $this->settleInfoId;
    }

    /**
     * @param int|mixed|null $settleInfoId
     */
    public function setSettleInfoId($settleInfoId): void
    {
        $this->settleInfoId = $settleInfoId;
    }

    /**
     * @return int|mixed|null
     */
    public function getProviderMerchantId()
    {
        return $this->providerMerchantId;
    }

    /**
     * @param int|mixed|null $providerMerchantId
     */
    public function setProviderMerchantId($providerMerchantId): void
    {
        $this->providerMerchantId = $providerMerchantId;
    }

    /**
     * @return int|mixed|null
     */
    public function getPaymentMode()
    {
        return $this->paymentMode;
    }

    /**
     * @param int|mixed|null $paymentMode
     */
    public function setPaymentMode($paymentMode): void
    {
        $this->paymentMode = $paymentMode;
    }

    /**
     * @return int|mixed|null
     */
    public function getPaymentMethod()
    {
        return $this->paymentMethod;
    }

    /**
     * @param int|mixed|null $paymentMethod
     */
    public function setPaymentMethod($paymentMethod): void
    {
        $this->paymentMethod = $paymentMethod;
    }

    /**
     * @return mixed|string|null
     */
    public function getAlipayAccount()
    {
        return $this->alipayAccount;
    }

    /**
     * @param mixed|string|null $alipayAccount
     */
    public function setAlipayAccount($alipayAccount): void
    {
        $this->alipayAccount = $alipayAccount;
    }

    /**
     * @return mixed|string|null
     */
    public function getAlipayName()
    {
        return $this->alipayName;
    }

    /**
     * @param mixed|string|null $alipayName
     */
    public function setAlipayName($alipayName): void
    {
        $this->alipayName = $alipayName;
    }

    /**
     * @return mixed|string|null
     */
    public function getWechatPay()
    {
        return $this->wechatPay;
    }

    /**
     * @param mixed|string|null $wechatPay
     */
    public function setWechatPay($wechatPay): void
    {
        $this->wechatPay = $wechatPay;
    }

    /**
     * @return mixed|string|null
     */
    public function getOpeningBank()
    {
        return $this->openingBank;
    }

    /**
     * @param mixed|string|null $openingBank
     */
    public function setOpeningBank($openingBank): void
    {
        $this->openingBank = $openingBank;
    }

    /**
     * @return mixed|string|null
     */
    public function getBankAccount()
    {
        return $this->bankAccount;
    }

    /**
     * @param mixed|string|null $bankAccount
     */
    public function setBankAccount($bankAccount): void
    {
        $this->bankAccount = $bankAccount;
    }

    /**
     * @return mixed|string|null
     */
    public function getBankName()
    {
        return $this->bankName;
    }

    /**
     * @param mixed|string|null $bankName
     */
    public function setBankName($bankName): void
    {
        $this->bankName = $bankName;
    }

    /**
     * @return mixed|string|null
     */
    public function getBankCode()
    {
        return $this->bankCode;
    }

    /**
     * @param mixed|string|null $bankCode
     */
    public function setBankCode($bankCode): void
    {
        $this->bankCode = $bankCode;
    }

    /**
     * @return int|mixed|null
     */
    public function getInvoicePaymentOrder()
    {
        return $this->invoicePaymentOrder;
    }

    /**
     * @param int|mixed|null $invoicePaymentOrder
     */
    public function setInvoicePaymentOrder($invoicePaymentOrder): void
    {
        $this->invoicePaymentOrder = $invoicePaymentOrder;
    }

    /**
     * @return float|mixed|null
     */
    public function getTax()
    {
        return $this->tax;
    }

    /**
     * @param float|mixed|null $tax
     */
    public function setTax($tax): void
    {
        $this->tax = $tax;
    }

    /**
     * @return mixed|string|null
     */
    public function getIdCard()
    {
        return $this->idCard;
    }

    /**
     * @param mixed|string|null $idCard
     */
    public function setIdCard($idCard): void
    {
        $this->idCard = $idCard;
    }

    /**
     * @return int|mixed|null
     */
    public function getIsVoid()
    {
        return $this->isVoid;
    }

    /**
     * @param int|mixed|null $isVoid
     */
    public function setIsVoid($isVoid): void
    {
        $this->isVoid = $isVoid;
    }

    /**
     * @return mixed|string|null
     */
    public function getInvoiceTax()
    {
        return $this->invoiceTax;
    }

    /**
     * @param mixed|string|null $invoiceTax
     */
    public function setInvoiceTax($invoiceTax): void
    {
        $this->invoiceTax = $invoiceTax;
    }

    /**
     * @return mixed|string|null
     */
    public function getCompanyId()
    {
        return $this->companyId;
    }

    /**
     * @param mixed|string|null $companyId
     */
    public function setCompanyId($companyId): void
    {
        $this->companyId = $companyId;
    }

    /**
     * @return int|mixed|null
     */
    public function getPaymentPeriod()
    {
        return $this->paymentPeriod;
    }

    /**
     * @param int|mixed|null $paymentPeriod
     */
    public function setPaymentPeriod($paymentPeriod): void
    {
        $this->paymentPeriod = $paymentPeriod;
    }

    /**
     * @return int|mixed|null
     */
    public function getTicketType()
    {
        return $this->ticketType;
    }

    /**
     * @param int|mixed|null $ticketType
     */
    public function setTicketType($ticketType): void
    {
        $this->ticketType = $ticketType;
    }

    /**
     * @return mixed|string|null
     */
    public function getPayeeMobile()
    {
        return $this->payeeMobile;
    }

    /**
     * @param mixed|string|null $payeeMobile
     */
    public function setPayeeMobile($payeeMobile): void
    {
        $this->payeeMobile = $payeeMobile;
    }
}
