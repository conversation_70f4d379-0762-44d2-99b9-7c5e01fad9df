<?php

namespace App\Entities\ProviderMerchantPayment;

class CreateFlowProcessorEntity
{
    /**
     * @var int 申请类型
     */
    private $applicationType;
    /**
     * @var int 单据类型
     */
    private $flowType;
    /**
     * @var int crm账号id
     */
    private $crmAccountId;
    /**
     * @var int|null 结算id
     */
    private $orderSettleId;
    /**
     * @var int|null 核销id
     */
    private $reconciliationId;
    public function getApplicationType(): int
    {
        return $this->applicationType;
    }

    public function setApplicationType(int $applicationType): void
    {
        $this->applicationType = $applicationType;
    }

    public function getFlowType(): int
    {
        return $this->flowType;
    }

    public function setFlowType(int $flowType): void
    {
        $this->flowType = $flowType;
    }

    public function getCrmAccountId(): int
    {
        return $this->crmAccountId;
    }

    public function setCrmAccountId(int $crmAccountId): void
    {
        $this->crmAccountId = $crmAccountId;
    }

    public function getOrderSettleId(): ?int
    {
        return $this->orderSettleId;
    }

    public function setOrderSettleId(?int $orderSettleId): void
    {
        $this->orderSettleId = $orderSettleId;
    }

    public function getReconciliationId(): ?int
    {
        return $this->reconciliationId;
    }

    public function setReconciliationId(?int $reconciliationId): void
    {
        $this->reconciliationId = $reconciliationId;
    }
}