<?php

namespace App\Entities\ProviderMerchantPayment;

class ProviderMerchantInvoiceDTO
{
    /**
     * @var int
     */
    private $invoiceId;
    /**
     * @var string
     */
    private $buyerName;
    /**
     * @var string
     */
    private $sellerName;
    /**
     * @var int
     */
    private $invoiceType;
    /**
     * @var float
     */
    private $invoiceAmount;
    /**
     * @var string
     */
    private $fileUrl;
    /**
     * @var float
     */
    private $taxRate;
    /**
     * @var string
     */
    private $invoiceNumber;
    /**
     * @var string
     */
    private $invoiceCode;
    /**
     * @var string
     */
    private $invoiceTime;
    /**
     * @var string
     */
    private $buyerTaxNumber;

    public function __construct($invoice)
    {
        $this->setInvoiceId(isset($invoice['id']) ?? 0);
        $this->setBuyerName($invoice['buyer_name'] ?? '');
        $this->setSellerName($invoice['seller_name'] ?? '');
        $this->setInvoiceType($invoice['invoice_type'] ?? 0);
        $this->setInvoiceAmount($invoice['invoice_amount'] ?? 0);
        $this->setFileUrl($invoice['file_url'] ?? '');
        $this->setTaxRate($invoice['tax_rate'] ?? 0.00);
        $this->setInvoiceNumber($invoice['invoice_number'] ?? '');
        $this->setInvoiceCode($invoice['invoice_code'] ?? '');
        $this->setInvoiceTime($invoice['invoice_time'] ?? '');
        $this->setBuyerTaxNumber($invoice['buyer_tax_number'] ?? '');
    }

    public function getInvoiceId(): int
    {
        return $this->invoiceId;
    }

    public function setInvoiceId(int $invoiceId): void
    {
        $this->invoiceId = $invoiceId;
    }

    public function getBuyerName(): string
    {
        return $this->buyerName;
    }

    public function setBuyerName(string $buyerName): void
    {
        $this->buyerName = $buyerName;
    }

    public function getSellerName(): string
    {
        return $this->sellerName;
    }

    public function setSellerName(string $sellerName): void
    {
        $this->sellerName = $sellerName;
    }

    public function getInvoiceType(): int
    {
        return $this->invoiceType;
    }

    public function setInvoiceType(int $invoiceType): void
    {
        $this->invoiceType = $invoiceType;
    }

    public function getInvoiceAmount(): float
    {
        return $this->invoiceAmount;
    }

    public function setInvoiceAmount(float $invoiceAmount): void
    {
        $this->invoiceAmount = $invoiceAmount;
    }

    public function getFileUrl(): string
    {
        return $this->fileUrl;
    }

    public function setFileUrl(string $fileUrl): void
    {
        $this->fileUrl = $fileUrl;
    }

    public function getTaxRate(): float
    {
        return $this->taxRate;
    }

    public function setTaxRate(float $taxRate): void
    {
        $this->taxRate = $taxRate;
    }

    public function getInvoiceNumber(): string
    {
        return $this->invoiceNumber;
    }

    public function setInvoiceNumber(string $invoiceNumber): void
    {
        $this->invoiceNumber = $invoiceNumber;
    }

    public function getInvoiceCode(): string
    {
        return $this->invoiceCode;
    }

    public function setInvoiceCode(string $invoiceCode): void
    {
        $this->invoiceCode = $invoiceCode;
    }

    public function getInvoiceTime(): string
    {
        return $this->invoiceTime;
    }

    public function setInvoiceTime(string $invoiceTime): void
    {
        $this->invoiceTime = $invoiceTime;
    }

    public function getBuyerTaxNumber(): string
    {
        return $this->buyerTaxNumber;
    }

    public function setBuyerTaxNumber(string $buyerTaxNumber): void
    {
        $this->buyerTaxNumber = $buyerTaxNumber;
    }
}