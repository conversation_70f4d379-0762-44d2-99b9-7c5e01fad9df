<?php

namespace App\Repositories\Account;

use App\Models\AccountEmail;

class AccountEmailRepository
{
    public function getAccountEmailsByAccountId($accountId)
    {
        return AccountEmail::query()
            ->where('accountid', $accountId)
            ->where('isvoid', 0)
            ->select(['email'])
            ->get();
    }

    public function emailExists($accountId, $email): bool
    {
        return AccountEmail::query()
            ->where('accountid', $accountId)
            ->where('isvoid', 0)
            ->where('email', $email)
            ->exists();
    }

    public function insert($data): bool
    {
        return AccountEmail::query()->insert($data);
    }
}