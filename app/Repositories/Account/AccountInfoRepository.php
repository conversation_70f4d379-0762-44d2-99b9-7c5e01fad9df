<?php

namespace App\Repositories\Account;

use App\Models\AccountInfo;

class AccountInfoRepository
{
    public function getAccountInfo($accountId)
    {
        return AccountInfo::query()->where('accountid', $accountId)
            ->select(['province', 'city', 'email'])->first() ?? [];
    }

    public function getAccountName($accountId)
    {
        return AccountInfo::query()->where('accountid', $accountId)
            ->value('contacter');
    }
}