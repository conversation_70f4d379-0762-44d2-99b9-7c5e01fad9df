<?php

namespace App\Repositories\Ekuaibao;

use App\Models\EkuaibaoCompany;

class EkuaibaoCompanyRepository
{
    public function getCompanies()
    {
        return EkuaibaoCompany::select('code', 'company')->get();
    }
    public function getCompanyNameByCode($ekuaibaoCode)
    {
        return EkuaibaoCompany::whereCode($ekuaibaoCode)->value('company');
    }
    public function getCompanyNameMapByCodes($ekuaibaoCodes)
    {
        return EkuaibaoCompany::whereIn('code', $ekuaibaoCodes)->pluck('company', 'code');
    }

    public function getCompanyNameMap()
    {
        return EkuaibaoCompany::pluck('company', 'code');
    }
}