<?php

namespace App\Repositories\ProviderMerchant;

use App\Models\ProviderMerchant;

class ProviderMerchantRepository
{
    public function getProviderMerchantsByProviderMerchantIds($providerMerchantIds, $excludeVoid = true)
    {
        return ProviderMerchant::whereIn('providermerchantid', $providerMerchantIds)
            ->when($excludeVoid, function ($query) {
                return $query->whereIsvoid(0);
            })
            ->select('providermerchantid', 'character', 'merchantname', 'idcardname', 'contacter', 'merchantid')
            ->get();
    }

    public function getProviderMerchantByProviderMerchantId($providerMerchantId)
    {
        return ProviderMerchant::where('providermerchantid', $providerMerchantId)
            ->select('providermerchantid', 'character', 'merchantname', 'idcardname', 'contacter')
            ->first();
    }

    public function getProviderMerchantsByContactName($contactName)
    {
        return ProviderMerchant::where('contacter', 'like', "%$contactName%")
            ->select('providermerchantid')
            ->get();
    }

    public function getProviderMerchantsByMerchantName($merchantName)
    {
        return ProviderMerchant::where('merchantname', 'like', "%$merchantName%")
            ->select('providermerchantid')
            ->get();
    }
}