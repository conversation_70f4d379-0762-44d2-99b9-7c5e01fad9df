<?php
/**
 * Copyright (C) HangZhou YanQu Network Technology Co., Ltd.
 * All rights reserved.
 *
 * 版权所有 （C）杭州研趣信息技术有限公司
 *
 * @copyright  Copyright (c) 2025 (https://www.shiyanjia.com)
 * <AUTHOR> 2025/1/22 16:30
 */
namespace App\Repositories\Order;

use App\Models\OrderAllot;

class OrderAllotRepository
{
    /**
     * 获取订单分派的测试项目信息
     * @param $oid
     * @return mixed
     */
    public function getProductInfo($oid)
    {
        return OrderAllot::query()
            ->with([
                'sendProduct:providerproductid,pname'
            ])
            ->where('oid', $oid)
            ->select('sendproductid')
            ->first();
    }

    public function getProductInfos($oids)
    {
        return OrderAllot::query()
            ->with([
                'sendProduct:providerproductid,pname'
            ])
            ->whereIn('oid', $oids)
            ->select('sendproductid', 'oid', 'settleamount')
            ->get();
    }

    /**
     * 获取订单对接人
     * @param $oid
     * @return OrderAllot|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|\Illuminate\Database\Query\Builder|object|null
     */
    public function getOrderPickup($oid)
    {
        return OrderAllot::query()
            ->with('orderPickUp')
            ->where('oid', $oid)
            ->select('pickup')
            ->first();
    }

    public function getAllotInfoByAllotID($allotID,$fields = ['oid'])
    {
        return OrderAllot::query()
            ->where('allotid', $allotID)
            ->select($fields)
            ->first();
    }
}