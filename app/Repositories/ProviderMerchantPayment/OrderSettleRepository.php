<?php

namespace App\Repositories\ProviderMerchantPayment;

use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceConstants;
use App\Models\OrderSettle;
use App\Models\OrderSettleItem;
use App\Models\ProviderMerchantInvoiceToPaymentRelation;
use App\Repositories\ProviderMerchant\ProviderMerchantRepository;
use Illuminate\Database\Eloquent\Builder;

class OrderSettleRepository
{
    public function getProviderMerchantIdMapBySettleIds($settleIds)
    {
        return OrderSettle::leftJoin('provider_merchant',
            'order_settle.providermerchantid', '=', 'provider_merchant.providermerchantid')
            ->whereIn('order_settle.settleid', $settleIds)
            ->pluck('provider_merchant.contacter', 'order_settle.settleid');
    }

    public function getOrderSettleBySettleId($settleId)
    {
        return OrderSettle::where('settleid', $settleId)
            ->where('isvoid', 0)
            ->select('settleid', 'applyamt', 'prestore', 'dealstatus', 'acceptbankinfo', 'merchantsettleinfoid',
                'ekuaibao_flow_operator_crm_account_id', 'ekuaibao_flow_code', 'orderids', 'supplierid',
                'ekuaibao_flow_id', 'invoice_payment_order')
            ->first();
    }

    public function getOrderSettlesByIds($settleIds)
    {
        return OrderSettle::whereIsvoid(0)
            ->whereIn('settleid', $settleIds)
            ->select('settleid', 'applyamt', 'prestore', 'dealstatus', 'invoice_status', 'supplierid',
                'merchantsettleinfoid', 'company_id', 'acceptbankinfo', 'ekuaibao_flow_id', 'postime')
            ->get();
    }

    public function getOrderSettlesWithProviderMerchantByIds(array $settleIds)
    {
        return OrderSettle::with(['providerMerchant' => function ($query) {
            $query->select('providermerchantid', 'contacter');
        }])
            ->whereIsvoid(0)
            ->whereIn('settleid', $settleIds)
            ->select('supplierid', 'settleid', 'applyamt')
            ->get();
    }


    public function getOrderSettleByFlowId($flowId)
    {
        return OrderSettle::where('ekuaibao_flow_id', $flowId)
            ->where('isvoid', 0)
            ->select('application_number', 'applyamt', 'prestore', 'dealstatus', 'settleid')
            ->first();
    }

    public function getInvoiceIdsBySettleId($settleId): array
    {
        return ProviderMerchantInvoiceToPaymentRelation::whereIsvoid(0)
            ->where('relation_type', ProviderMerchantInvoiceConstants::RELATE_TO_PAYMENT_TYPE_SETTLE)
            ->where('related_application_id', $settleId)
            ->pluck('provider_merchant_invoice_id')->toArray();
    }

    public function getOrderSettlesBuilder($params)
    {
        return OrderSettle::where('isvoid', 0)
            ->when(!empty($params['provider_merchant_contact_name']), function (Builder $query) use ($params) {
                $providerMerchants = app(ProviderMerchantRepository::class)
                    ->getProviderMerchantsByContactName($params['provider_merchant_contact_name']);
                $providerMerchantIds = $providerMerchants->pluck('providermerchantid')->toArray();
                $query->whereIn('supplierid', $providerMerchantIds);
            })
            ->when(!empty($params['provider_merchant_name']), function (Builder $query) use ($params) {
                $providerMerchants = app(ProviderMerchantRepository::class)
                    ->getProviderMerchantsByMerchantName($params['provider_merchant_name']);
                $providerMerchantIds = $providerMerchants->pluck('providermerchantid')->toArray();
                $query->whereIn('supplierid', $providerMerchantIds);
            })
            ->when(!empty($params['invoice_statuses']), function (Builder $query) use ($params) {
                $query->whereIn('invoice_status', $params['invoice_statuses']);
            })
            ->when(!empty($params['deal_statuses']), function (Builder $query) use ($params) {
                $query->whereIn('dealstatus', $params['deal_statuses']);
            })
            ->when(!empty($params['min_apply_time']), function (Builder $query) use ($params) {
                $query->where('postime', '>=', $params['min_apply_time']);
            })
            ->when(isset($params['invoice_payment_order']), function (Builder $query) use ($params) {
                $query->where('invoice_payment_order', $params['invoice_payment_order']);
            })
            ->when(!empty($params['order_settle_ids']), function (Builder $query) use ($params) {
                $query->whereIn('settleid', $params['order_settle_ids']);
            });
    }

    public function getInvoiceRelatedAmountMapBySettleIds($settleIds): \Illuminate\Support\Collection
    {
        return ProviderMerchantInvoiceToPaymentRelation::whereIsvoid(0)
            ->where('relation_type', ProviderMerchantInvoiceConstants::RELATE_TO_PAYMENT_TYPE_SETTLE)
            ->whereIn('related_application_id', $settleIds)
            ->selectRaw('related_application_id, sum(relate_amount) as related_amount_sum')
            ->groupBy('related_application_id')->pluck('related_amount_sum', 'related_application_id');
    }

    public function getSettleItemAmountSumBySettleId($settleId)
    {
        $settle = $this->getOrderSettleBySettleId($settleId);
        $orderIds = $settle->orderids ?? '';
        $orderIds = explode(',', $orderIds);
        $settleAmounts = OrderSettleItem::whereIsvoid(0)->whereIn('orderid', $orderIds)
            ->pluck('settleamt');
        return $settleAmounts->sum();
    }

    public function updateEkuaibaoFlowAndOperator($settleId, $ekuaibaoFlowCode, $flowId, $operatorCrmAccountId)
    {
        OrderSettle::where('settleid', $settleId)
            ->where('isvoid', 0)
            ->update([
                'ekuaibao_flow_code' => $ekuaibaoFlowCode,
                'ekuaibao_flow_id' => $flowId,
                'ekuaibao_flow_operator_crm_account_id' => $operatorCrmAccountId
            ]);
    }

    public function updateInvoiceStatus(int $settleId, int $invoiceStatus): void
    {
        OrderSettle::where('settleid', $settleId)
            ->where('isvoid', 0)
            ->update(['invoice_status' => $invoiceStatus]);
    }

    public function updateInvoice(int $settleId, int $invoice): void
    {
        OrderSettle::where('settleid', $settleId)
            ->where('isvoid', 0)
            ->update(['invoice' => $invoice]);
    }
}