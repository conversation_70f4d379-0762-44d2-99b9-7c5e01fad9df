<?php

namespace App\Repositories\ProviderMerchantPayment;

use App\Models\ProviderMerchantSettleInfo;

class ProviderMerchantSettleInfoRepository
{
    public function getSettleInfosByProviderMerchantIds($providerMerchantIds)
    {
        return ProviderMerchantSettleInfo::whereIsvoid(0)
            ->whereIn('providermerchantid', $providerMerchantIds)
            ->select('providermerchantid', 'bankname', 'bankaccount')
            ->get();
    }

    public function getSettleInfoById($settleInfoId)
    {
        return ProviderMerchantSettleInfo::whereIsvoid(0)
            ->where('merchantsettleinfoid', $settleInfoId)
            ->select('bankaccount', 'paymentmethod', 'paymentmode', 'bankname', 'providermerchantid', 'winfksort')
            ->first();
    }

    public function getSettleInfosByIds($settleInfoIds)
    {
        return ProviderMerchantSettleInfo::whereIsvoid(0)
            ->whereIn('merchantsettleinfoid', $settleInfoIds)
            ->select('bankaccount', 'paymentmethod', 'paymentmode', 'bankname')
            ->get();
    }
}