<?php

namespace App\Repositories\ProviderMerchantPayment;

use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants;
use App\Models\ProviderMerchant;
use App\Models\ProviderMerchantInvoiceReconciliationApplication;
use App\Models\ProviderMerchantInvoiceReconciliationToPaymentRelation;
use App\Models\ProviderMerchantInvoiceToReconciliationRelation;
use Illuminate\Database\Eloquent\Builder;

class InvoiceReconciliationApplicationRepository
{
    public function getInvoiceIdsByReconciliationId($reconciliationApplicationId)
    {
        return ProviderMerchantInvoiceToReconciliationRelation::whereIsvoid(0)
            ->where('reconciliation_application_id', $reconciliationApplicationId)
            ->pluck('provider_merchant_invoice_id')->toArray();
    }

    public function getReconciliationApplicationById($reconciliationApplicationId)
    {
        return ProviderMerchantInvoiceReconciliationApplication::whereIsvoid(0)
            ->where('id', $reconciliationApplicationId)
            ->select('id', 'process_status', 'reconciliation_amount', 'ekuaibao_flow_operator_crm_account_id',
                'ekuaibao_flow_code', 'provider_merchant_subject_name', 'ekuaibao_company_code', 'settle_infos_json',
                'settle_info_id', 'reconciliation_type')
            ->first();
    }

    public function getReconciliationApplicationByFlowId($flowId)
    {
        return ProviderMerchantInvoiceReconciliationApplication::whereIsvoid(0)
            ->where('ekuaibao_flow_id', $flowId)
            ->select('id', 'process_status', 'reconciliation_amount')
            ->first();
    }

    public function getReconciliationApplicationsByIds($reconciliationApplicationIds)
    {
        return ProviderMerchantInvoiceReconciliationApplication::whereIsvoid(0)
            ->whereIn('id', $reconciliationApplicationIds)
            ->select('id', 'ekuaibao_flow_code', 'process_status')->get();
    }

    public function getPaymentRelationsByProviderMerchantIds($providerMerchantIds)
    {
        return ProviderMerchantInvoiceReconciliationToPaymentRelation::whereIsvoid(0)
            ->whereIn('provider_merchant_id', $providerMerchantIds)
            ->select('reconciliation_application_id')
            ->get();
    }

    public function getPaymentRelationsByReconciliationIds($reconciliationApplicationIds)
    {
        return ProviderMerchantInvoiceReconciliationToPaymentRelation::whereIsvoid(0)
            ->whereIn('reconciliation_application_id', $reconciliationApplicationIds)
            ->select('reconciliation_application_id', 'related_application_id', 'relation_type',
                'provider_merchant_id')
            ->get();
    }

    public function getPaymentRelationsByReconciliationId($reconciliationApplicationId)
    {
        return ProviderMerchantInvoiceReconciliationToPaymentRelation::whereIsvoid(0)
            ->where('reconciliation_application_id', $reconciliationApplicationId)
            ->select('reconciliation_application_id', 'related_application_id', 'relation_type',
                'provider_merchant_id')
            ->get();
    }

    /**
     * @param array $filter
     * @return Builder
     */
    public function getReconciliationApplicationsBuilder(array $filter = []): Builder
    {
        return ProviderMerchantInvoiceReconciliationApplication::whereIsvoid(0)
            ->when(!empty($filter['reconcile_source_id']), function (Builder $query) use ($filter) {
                $reconcileApplicationIds = ProviderMerchantInvoiceReconciliationToPaymentRelation::whereIsvoid(0)
                    ->where('related_application_id', $filter['reconcile_source_id'])
                    ->pluck('reconciliation_application_id')->toArray();
                if (!empty($reconcileApplicationIds)) {
                    return $query->whereIn('id', $reconcileApplicationIds);
                } else {
                    return $query->where('id', 0);
                }
            })
            ->when(!empty($filter['provider_merchant_name']), function (Builder $query) use ($filter) {
                $providerMerchantIds = ProviderMerchant::where('contacter', 'like',
                    "%{$filter['provider_merchant_name']}%")
                    ->pluck('providermerchantid')->toArray();
                $reconcileApplicationIds = self::getPaymentRelationsByProviderMerchantIds($providerMerchantIds)
                    ->pluck('reconciliation_application_id')->toArray();
                if (!empty($reconcileApplicationIds)) {
                    return $query->whereIn('id', $reconcileApplicationIds);
                } else {
                    return $query->where('id', 0);
                }
            })
            ->when(!empty($filter['provider_merchant_subject_name']), function (Builder $query) use ($filter) {
                return $query->where('provider_merchant_subject_name', 'like',
                    "%{$filter['provider_merchant_subject_name']}%");
            })
            ->when(!empty($filter['apply_crm_account_id']), function (Builder $query) use ($filter) {
                return $query->where('apply_crm_account_id',
                    $filter['apply_crm_account_id']);
            })
            ->when(!empty($filter['min_apply_time']), function (Builder $query) use ($filter) {
                return $query->where('create_time', '>=',
                    $filter['min_apply_time']);
            })
            ->when(!empty($filter['max_apply_time']), function (Builder $query) use ($filter) {
                $filter['max_apply_time'] = date('Y-m-d 23:59:59', strtotime($filter['max_apply_time']));
                return $query->where('create_time', '<',
                    $filter['max_apply_time']);
            })
            ->when(!empty($filter['reconciliation_type']), function (Builder $query) use ($filter) {
                return $query->where('reconciliation_type',
                    $filter['reconciliation_type']);
            })
            ->when(!empty($filter['reconcile_status']), function (Builder $query) use ($filter) {
                return $query->where('process_status',
                    $filter['reconcile_status']);
            })
            ->when(!empty($filter['reconcile_statuses']), function (Builder $query) use ($filter) {
                return $query->whereIn('process_status',
                    $filter['reconcile_statuses']);
            })
            ->when(!empty($filter['ekuaibao_flow_code']), function (Builder $query) use ($filter) {
                return $query->where('ekuaibao_flow_code',
                    $filter['ekuaibao_flow_code']);
            })->when(!empty($filter['reconciliation_application_id']), function (Builder $query) use ($filter) {
                return $query->where('id', $filter['reconciliation_application_id']);
            });
    }

    public function insertReconciliation($insertReconciliation)
    {
        return ProviderMerchantInvoiceReconciliationApplication::insertGetId($insertReconciliation);
    }

    public function updateReconciliationStatus($reconciliationApplicationId, $status)
    {
        ProviderMerchantInvoiceReconciliationApplication::whereIsvoid(0)
            ->where('id', $reconciliationApplicationId)
            ->update(['process_status' => $status]);
    }

    public function updateReconciliation($reconciliationId, $updateData)
    {
        ProviderMerchantInvoiceReconciliationApplication::whereIsvoid(0)
            ->where('id', $reconciliationId)
            ->update($updateData);
    }

    public function updateEkuaibaoFlowAndOperator($reconciliationApplicationId, $ekuaibaoFlowCode, $ekuaibaoFlowId,
                                                  $operatorCrmAccountId)
    {
        ProviderMerchantInvoiceReconciliationApplication::whereIsvoid(0)
            ->where('id', $reconciliationApplicationId)
            ->update([
                'ekuaibao_flow_code' => $ekuaibaoFlowCode,
                'ekuaibao_flow_id' => $ekuaibaoFlowId,
                'ekuaibao_flow_operator_crm_account_id' => $operatorCrmAccountId,
            ]);
    }

    public function insertReconciliationToInvoiceRelations($insertRelations)
    {
        return ProviderMerchantInvoiceToReconciliationRelation::insert($insertRelations);
    }

    public function insertReconciliationToPaymentRelations($insertRelations)
    {
        return ProviderMerchantInvoiceReconciliationToPaymentRelation::insert($insertRelations);
    }
}
