<?php

namespace App\Repositories\ProviderMerchantPayment;

use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceConstants;
use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants;
use App\Models\ProviderMerchantInvoice;
use App\Models\ProviderMerchantInvoiceToPaymentRelation;
use App\Models\ProviderMerchantInvoiceToReconciliationRelation;

class ProviderMerchantInvoiceRepository
{
    public function getPrepaymentIdsByInvoiceIds($invoiceIds)
    {
        return ProviderMerchantInvoiceToPaymentRelation::whereIsvoid(0)
            ->whereIn('provider_merchant_invoice_id', $invoiceIds)
            ->where('relation_type',
                ProviderMerchantInvoiceReconciliationConstants::RECONCILE_TYPE_PREPAYMENT)
            ->pluck('related_application_id')
            ->toArray();
    }

    public function getReconcileRelationsByInvoiceIds($invoiceIds)
    {
        return ProviderMerchantInvoiceToReconciliationRelation::whereIsvoid(0)
            ->whereIn('provider_merchant_invoice_id', $invoiceIds)
            ->select('provider_merchant_invoice_id', 'reconciliation_application_id')
            ->get();
    }

    public function getInvoicesByInvoiceIds($invoiceIds)
    {
        return ProviderMerchantInvoice::whereIsvoid(0)
            ->whereIn('id', $invoiceIds)
            ->select('id', 'buyer_name', 'seller_name', 'invoice_type', 'invoice_amount', 'file_url',
                'tax_rate', 'invoice_number', 'invoice_time')->get();
    }

    public function getInvoiceByInvoiceNumberAndCode($invoiceNumber, $invoiceCode)
    {
        return ProviderMerchantInvoice::whereIsvoid(0)
            ->where('invoice_number', $invoiceNumber)
            ->where('invoice_code', $invoiceCode)
            ->first();
    }

    public function getInvoicesByInvoiceNumbers($invoiceNumbers)
    {
        return ProviderMerchantInvoice::whereIsvoid(0)
            ->whereIn('invoice_number', $invoiceNumbers)
            ->select('id', 'invoice_number', 'invoice_code')->get();
    }

    public function getRelatedToPrepaymentInvoiceIdsByInvoiceIds($invoiceIds)
    {
        return ProviderMerchantInvoiceToPaymentRelation::from(
            'provider_merchant_invoice_to_payment_relation as relation')
            ->leftJoin('provider_prestore', 'relation.related_application_id', '=',
                'provider_prestore.prestoreid')
            ->where('relation.isvoid', 0)
            ->where('provider_prestore.isvoid', 0)
            ->whereIn('relation.provider_merchant_invoice_id', $invoiceIds)
            ->where('relation.relation_type', ProviderMerchantInvoiceConstants::RELATE_TO_PAYMENT_TYPE_PREPAYMENT)
            ->pluck('relation.provider_merchant_invoice_id')
            ->toArray();
    }

    public function getRelatedToPaymentInvoiceIdsByInvoiceIds($invoiceIds)
    {
        return ProviderMerchantInvoiceToPaymentRelation::whereIsvoid(0)
            ->whereIn('provider_merchant_invoice_id', $invoiceIds)
            ->pluck('provider_merchant_invoice_id')
            ->toArray();
    }

    public function deleteInvoiceToPrepaymentRelationByInvoiceIds($invoiceIds)
    {
        ProviderMerchantInvoiceToPaymentRelation::whereIsvoid(0)
            ->whereIn('provider_merchant_invoice_id', $invoiceIds)
            ->update(['isvoid' => 1]);
    }

    public function saveInvoiceToPrepaymentRelation($insertData)
    {
        ProviderMerchantInvoiceToPaymentRelation::insert($insertData);
    }

    public function saveInvoiceToPaymentRelation($insertData)
    {
        ProviderMerchantInvoiceToPaymentRelation::insert($insertData);
    }

    public function voidInvoiceToPrepaymentRelations($invoiceIds, $prepaymentIds)
    {
        ProviderMerchantInvoiceToPaymentRelation::whereIsvoid(0)
            ->where('relation_type', ProviderMerchantInvoiceConstants::RELATE_TO_PAYMENT_TYPE_PREPAYMENT)
            ->whereIn('provider_merchant_invoice_id', $invoiceIds)
            ->whereIn('related_application_id', $prepaymentIds)
            ->update(['isvoid' => 1]);
    }

    public function voidInvoiceToSettlementRelations($invoiceIds, $settlementIds)
    {
        ProviderMerchantInvoiceToPaymentRelation::whereIsvoid(0)
            ->where('relation_type', ProviderMerchantInvoiceConstants::RELATE_TO_PAYMENT_TYPE_SETTLE)
            ->whereIn('provider_merchant_invoice_id', $invoiceIds)
            ->whereIn('related_application_id', $settlementIds)
            ->update(['isvoid' => 1]);
    }
}
