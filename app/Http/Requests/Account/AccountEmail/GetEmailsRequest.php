<?php

namespace App\Http\Requests\Account\AccountEmail;

use App\Http\Requests\ApiRequest;

class GetEmailsRequest extends ApiRequest
{

    public function rules(): array
    {
        return [
            'account_id' => 'required|integer',
        ];
    }
    public function messages(): array
    {
        return [
            'account_id.required' => 'account_id不能为空',
            'account_id.integer' => 'account_id必须是整数',
        ];
    }
}