<?php

namespace App\Http\Requests\Account\AccountEmail;

use App\Http\Requests\ApiRequest;

class AddEmailRequest extends ApiRequest
{
    public function rules(): array
    {
        return [
            'account_id' => 'required|integer|min:1',
            'email' => 'required|email',
        ];
    }

    public function messages(): array
    {
        return [
            'account_id.required' => 'account_id不能为空',
            'account_id.integer' => 'account_id必须为整数',
            'account_id.min' => 'account_id必须大于0',
            'email.required' => '邮箱不能为空',
            'email.email' => '邮箱格式不正确',
        ];
    }
}