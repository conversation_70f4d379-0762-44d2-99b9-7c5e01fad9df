<?php

namespace App\Http\Requests\ProviderMerchantPayment\OrderSettle;

use App\Http\Requests\ApiRequest;

class SaveOrderSettleInvoicesRequest extends ApiRequest
{
    public function rules(): array
    {
        return [
            'crm_account_id' => '',
            'provider_merchant_id' => '',
            'order_settle_ids' => 'required|array',
            'invoices' => 'array',
            'invoices.*.file_url' => 'required',
            'invoices.*.invoice_type' => 'required',
            'invoices.*.invoice_number' => 'required',
            'invoices.*.buyer_name' => 'required',
            'invoices.*.seller_name' => 'required',
            'invoices.*.tax_inclusive_amount' => 'required',
            'invoices.*.tax_exclusive_amount' => 'required',
            'invoices.*.invoice_time' => 'required',
            'invoices.*.tax_rate' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'order_settle_ids.required' => '结算申请ids不能为空',
            'merge_invoice_prepayment_ids.array' => '结算申请ids必须是数组',
            'invoices.array' => '发票列表必须是数组',
            'invoices.*.file_url.required' => '发票文件地址不能为空',
            'invoices.*.invoice_type.required' => '发票类型不能为空',
            'invoices.*.invoice_number.required' => '发票号不能为空',
            'invoices.*.buyer_name.required' => '购买方名称不能为空',
            'invoices.*.seller_name.required' => '销售方名称不能为空',
            'invoices.*.tax_inclusive_amount.required' => '含税金额不能为空',
            'invoices.*.tax_exclusive_amount.required' => '不含税金额不能为空',
            'invoices.*.invoice_time.required' => '开票时间不能为空',
            'invoices.*.tax_rate.required' => '税率不能为空',
        ];
    }
}