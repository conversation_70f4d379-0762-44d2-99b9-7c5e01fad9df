<?php

namespace App\Http\Requests\ProviderMerchantPayment\OrderSettle;

use App\Http\Requests\ApiRequest;

class CheckInvoicesBeforeAddSettleRequest extends ApiRequest
{
    public function rules(): array
    {
        return [
            'apply_amount' => 'required|numeric|min:0',
            'prepayment_deducted_amount' => 'required|numeric|min:0',
            'provider_merchant_id' => 'required',
            'settle_account_id' => 'required',
            'invoices' => 'array',
            'invoices.*.file_url' => 'required',
            'invoices.*.invoice_type' => 'required',
            'invoices.*.invoice_number' => 'required',
            'invoices.*.buyer_name' => 'required',
            'invoices.*.seller_name' => 'required',
            'invoices.*.tax_inclusive_amount' => 'required',
            'invoices.*.tax_exclusive_amount' => 'required',
            'invoices.*.invoice_time' => 'required',
            'invoices.*.tax_rate' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'apply_amount.required' => '申请金额不能为空',
            'apply_amount.numeric' => '申请金额必须是数字',
            'apply_amount.min' => '申请金额不能小于0',
            'prepayment_deducted_amount.required' => '预付款抵扣金额不能为空',
            'prepayment_deducted_amount.numeric' => '预付款抵扣金额必须是数字',
            'prepayment_deducted_amount.min' => '预付款抵扣金额不能小于0',
            'provider_merchant_id.required' => '供应商ID不能为空',
            'settle_account_id.required' => '结算账户ID不能为空',
            'invoices.array' => '发票列表必须是数组',
            'invoices.*.file_url.required' => '发票文件地址不能为空',
            'invoices.*.invoice_type.required' => '发票类型不能为空',
            'invoices.*.invoice_number.required' => '发票号不能为空',
            'invoices.*.buyer_name.required' => '购买方名称不能为空',
            'invoices.*.seller_name.required' => '销售方名称不能为空',
            'invoices.*.tax_inclusive_amount.required' => '含税金额不能为空',
            'invoices.*.tax_exclusive_amount.required' => '不含税金额不能为空',
            'invoices.*.invoice_time.required' => '开票时间不能为空',
            'invoices.*.tax_rate.required' => '税率不能为空',
        ];
    }
}