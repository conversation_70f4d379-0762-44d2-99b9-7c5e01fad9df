<?php

namespace App\Http\Requests\ProviderMerchantPayment;

use App\Http\Requests\ApiRequest;

class SaveEkuaibaoFlowCodeRequest extends ApiRequest
{
    public function rules(): array
    {
        return [
            'ekuaibao_flow_code' => 'required',
            'prepayment_id' => 'nullable|integer',
            'reconciliation_application_id' => 'nullable|integer',
            'order_settle_id' => 'nullable|integer',
            'crm_account_id' => 'required|integer|gt:0',
        ];
    }

    public function messages(): array
    {
        return [
            'ekuaibao_flow_code.required' => '请填写易快报单号',
            'prepayment_id.integer' => '预付ID必须是整数',
            'reconciliation_application_id.integer' => '核销申请ID必须是整数',
            'order_settle_id.integer' => '结算申请ID必须是整数',
            'crm_account_id.required' => '请填写CRM账户ID',
            'crm_account_id.integer' => 'CRM账户ID必须是整数',
            'crm_account_id.gt' => 'CRM账户ID必须大于0',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $prepayment = (int)$this->input('prepayment_id');
            $reconciliation = (int)$this->input('reconciliation_application_id');
            $settle = (int)$this->input('order_settle_id');

            if ($prepayment <= 0 && $reconciliation <= 0 && $settle <= 0) {
                $validator->errors()->add('prepayment_id', '预存、核销申请和结算申请中，至少填写一个有效 ID。');
            }
        });
    }
}