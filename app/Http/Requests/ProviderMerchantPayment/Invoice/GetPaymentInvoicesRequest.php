<?php

namespace App\Http\Requests\ProviderMerchantPayment\Invoice;

use App\Http\Requests\ApiRequest;

class GetPaymentInvoicesRequest extends ApiRequest
{

    public function rules(): array
    {
        return [
            'prepayment_id' => 'required_without_all:reconciliation_application_id,order_settle_id|nullable|integer',
            'reconciliation_application_id' => 'required_without_all:prepayment_id,order_settle_id|nullable|integer',
            'order_settle_id' => 'required_without_all:prepayment_id,reconciliation_application_id|nullable|integer',
        ];
    }

    public function messages(): array
    {
        return [
            'prepayment_id.required_without_all' => '预付款id、核销申请id、结算id必须至少传一个',
            'reconciliation_application_id.required_without_all' => '预付款id、核销申请id、结算id必须至少传一个',
            'order_settle_id.required_without_all' => '预付款id、核销申请id、结算id必须至少传一个',
            'prepayment_id.integer' => '预付款id必须是整数',
            'reconciliation_application_id.integer' => '核销申请id必须是整数',
            'order_settle_id.integer' => '结算id必须是整数',
        ];
    }
}