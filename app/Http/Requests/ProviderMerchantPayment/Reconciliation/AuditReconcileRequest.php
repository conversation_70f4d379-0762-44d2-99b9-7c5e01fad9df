<?php

namespace App\Http\Requests\ProviderMerchantPayment\Reconciliation;

use App\Constants\ProviderMerchantPayment\ProviderMerchantInvoiceReconciliationConstants;
use App\Http\Requests\ApiRequest;

class AuditReconcileRequest extends ApiRequest
{
    public function rules(): array
    {
        $auditResultMap = array_keys(ProviderMerchantInvoiceReconciliationConstants::AUDIT_RESULT_MAP);
        return [
            'crm_account_id' => 'required|integer',
            'reconciliation_id' => 'required|integer',
            'audit_result' => 'required|integer:in' . implode(',', $auditResultMap),
        ];
    }

    public function messages(): array
    {
        return [
            'crm_account_id.required' => '操作人id不能为空',
            'crm_account_id.integer' => '操作人id必须是整数',
            'reconciliation_id.required' => '核销申请id不能为空',
            'reconciliation_id.integer' => '核销申请id必须是整数',
            'audit_result.required' => '审核结果不能为空',
            'audit_result.integer' => '审核结果必须是整数',
            'audit_result.in' => '审核结果不合法',
        ];
    }
}