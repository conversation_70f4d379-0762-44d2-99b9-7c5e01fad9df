<?php

namespace App\Http\Controllers;

use App\Services\SmartCall\SmartCallService;
use App\Utils\StatusCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * 智能外呼系统控制器
 */
class SmartCallController extends Controller
{
    /**
     * 智能外呼服务
     */
    private SmartCallService $smartCallService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->smartCallService = new SmartCallService();
    }

    /**
     * 导入客户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function importCustomers(Request $request)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'customers' => 'required|array',
                'customers.*.name' => 'required|string|max:100',
                'customers.*.phone' => 'required|string|max:20',
                'customers.*.task_id' => 'required|string',
                'encrypt_key' => 'nullable|string|size:16'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $customers = $request->input('customers');
            $encryptKey = $request->input('encrypt_key');

            // 检查是否为批量导入
            if (count($customers) > 100) {
                $result = $this->smartCallService->batchImportCustomers($customers, $encryptKey);
            } else {
                $result = $this->smartCallService->importCustomers($customers, $encryptKey);
            }

            return $this->createResponse(StatusCode::OK, $result, '客户导入成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '客户导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建外呼任务
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createCallTask(Request $request)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'task_name' => 'required|string|max:200',
                'template_id' => 'required|string',
                'call_time_start' => 'nullable|string',
                'call_time_end' => 'nullable|string',
                'max_call_count' => 'nullable|integer|min:1',
                'call_interval' => 'nullable|integer|min:1'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $taskData = $request->only([
                'task_name', 'template_id', 'call_time_start',
                'call_time_end', 'max_call_count', 'call_interval'
            ]);

            $result = $this->smartCallService->createCallTask($taskData);

            return $this->createResponse(StatusCode::OK, $result, '外呼任务创建成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '外呼任务创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 查询任务状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTaskStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'task_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $taskId = $request->input('task_id');
            $result = $this->smartCallService->getTaskStatus($taskId);

            return $this->createResponse(StatusCode::OK, $result, '查询成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '查询任务状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 查询外呼结果
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCallResults(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'task_id' => 'required|string',
                'page' => 'nullable|integer|min:1',
                'page_size' => 'nullable|integer|min:1|max:1000',
                'call_status' => 'nullable|string',
                'start_time' => 'nullable|string',
                'end_time' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $taskId = $request->input('task_id');
            $filters = $request->only(['page', 'page_size', 'call_status', 'start_time', 'end_time']);

            $result = $this->smartCallService->getCallResults($taskId, $filters);

            return $this->createResponse(StatusCode::OK, $result, '查询成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '查询外呼结果失败: ' . $e->getMessage());
        }
    }

    /**
     * 暂停外呼任务
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function pauseTask(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'task_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $taskId = $request->input('task_id');
            $result = $this->smartCallService->pauseTask($taskId);

            return $this->createResponse(StatusCode::OK, $result, '任务暂停成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '任务暂停失败: ' . $e->getMessage());
        }
    }

    /**
     * 恢复外呼任务
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resumeTask(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'task_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $taskId = $request->input('task_id');
            $result = $this->smartCallService->resumeTask($taskId);

            return $this->createResponse(StatusCode::OK, $result, '任务恢复成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '任务恢复失败: ' . $e->getMessage());
        }
    }

    /**
     * 停止外呼任务
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stopTask(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'task_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $taskId = $request->input('task_id');
            $result = $this->smartCallService->stopTask($taskId);

            return $this->createResponse(StatusCode::OK, $result, '任务停止成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '任务停止失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取通话录音
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCallRecording(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'call_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $callId = $request->input('call_id');
            $result = $this->smartCallService->getCallRecording($callId);

            return $this->createResponse(StatusCode::OK, $result, '获取录音成功');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '获取录音失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试加密解密功能
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testEncryption(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'data' => 'required|string',
                'encrypt_key' => 'required|string|size:16'
            ]);

            if ($validator->fails()) {
                return $this->createResponse(StatusCode::ERROR, [], $validator->errors()->first());
            }

            $data = $request->input('data');
            $encryptKey = $request->input('encrypt_key');

            // 创建客户端实例用于加密操作
            $client = new \App\Services\SmartCall\SmartCallClient();

            // 加密
            $encrypted = $client->encryptSensitiveField($data, $encryptKey);

            // 解密验证
            $decrypted = $client->decryptSensitiveField($encrypted, $encryptKey);

            $result = [
                'original' => $data,
                'encrypted' => $encrypted,
                'decrypted' => $decrypted,
                'md5_hash' => $client->getMd5Hash($data),
                'is_valid' => $data === $decrypted
            ];

            return $this->createResponse(StatusCode::OK, $result, '加密测试完成');

        } catch (\Exception $e) {
            return $this->createResponse(StatusCode::ERROR, [], '加密测试失败: ' . $e->getMessage());
        }
    }
}
