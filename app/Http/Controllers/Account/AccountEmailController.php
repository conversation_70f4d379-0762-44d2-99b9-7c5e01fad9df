<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Http\Requests\Account\AccountEmail\AddEmailRequest;
use App\Http\Requests\Account\AccountEmail\GetEmailsRequest;
use App\Services\Account\AccountEmailService;

class AccountEmailController extends Controller
{

    /**
     * 获取邮箱列表
     * @param GetEmailsRequest $request
     * @return array
     */
    public function getEmails(GetEmailsRequest $request)
    {
        $emails = app(AccountEmailService::class)->getEmails($request->all());
        return $this->success($emails);
    }

    /**
     * 新增邮箱
     * @param AddEmailRequest $request
     * @return array
     */
    public function addEmail(AddEmailRequest $request)
    {
        app(AccountEmailService::class)->addEmail($request->all());
        return $this->success();
    }
}