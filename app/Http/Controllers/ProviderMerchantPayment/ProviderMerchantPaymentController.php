<?php

namespace App\Http\Controllers\ProviderMerchantPayment;

use App\Exceptions\BusinessException;
use App\Exceptions\BusinessWithoutErrorReportException;
use App\Http\Requests\ProviderMerchantPayment\SaveEkuaibaoFlowCodeRequest;
use App\Services\ProviderMerchantPayment\ProviderMerchantPaymentService;
use App\Utils\StatusCode;
use Illuminate\Http\JsonResponse;
use Yanqu\YanquPhplib\Exception\CurlException;

class ProviderMerchantPaymentController
{
    private $providerMerchantPaymentService;
    public function __construct()
    {
        $this->providerMerchantPaymentService = new ProviderMerchantPaymentService();
    }

    /**
     * @param SaveEkuaibaoFlowCodeRequest $request
     * @return JsonResponse
     * @throws BusinessException|CurlException|BusinessWithoutErrorReportException
     */
    public function saveEkuaibaoFlowCode(SaveEkuaibaoFlowCodeRequest $request)
    {
        $this->providerMerchantPaymentService->saveEkuaibaoFlowCode($request->all());
        return response()->json([
            'status_code' => StatusCode::OK,
            'status_msg' => 'success',
            'data' => [],
        ]);
    }
}