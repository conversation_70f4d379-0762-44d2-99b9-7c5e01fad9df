<?php

namespace App\Http\Controllers\ProviderMerchantPayment;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProviderMerchantPayment\Invoice\GetPaymentInvoicesRequest;
use App\Services\ProviderMerchantPayment\Invoice\ProviderMerchantInvoiceService;
use App\Utils\StatusCode;

class ProviderMerchantInvoiceController extends Controller
{
    private $providerMerchantInvoiceService;

    public function __construct()
    {
        $this->providerMerchantInvoiceService = new ProviderMerchantInvoiceService();
    }

    public function getPaymentInvoices(GetPaymentInvoicesRequest $request)
    {
        $list = $this->providerMerchantInvoiceService->getPaymentInvoices($request->all());
        return response()->json([
            'status_code' => StatusCode::OK,
            'status_msg' => 'success',
            'data' => $list,
        ]);
    }
}