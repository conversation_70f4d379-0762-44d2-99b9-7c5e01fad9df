<?php

namespace App\Utils\SmartCall;

/**
 * 智能外呼系统AES加密工具类
 * 基于文档中的AES/ECB/PKCS5Padding加密方式
 */
class SmartCallAESUtil
{
    /**
     * 安全随机数生成方式
     */
    private const SECURE_ALGORITHM = 'SHA1PRNG';
    
    /**
     * 密钥算法
     */
    private const ALGORITHM = 'AES';
    
    /**
     * 密钥长度
     */
    private const LENGTH = 128;
    
    /**
     * 加密/解密算法-工作模式-填充模式
     */
    private const CIPHER_ALGORITHM = 'AES-128-ECB';
    
    /**
     * AES加密
     *
     * @param string $data 待加密数据
     * @param string $password 密钥（16位）
     * @return string 加密后的Base64字符串
     * @throws \Exception
     */
    public static function encrypt(string $data, string $password): string
    {
        if (strlen($password) !== 16) {
            throw new \Exception('密钥长度必须为16位');
        }
        
        $key = self::generateKey($password);
        $encrypted = openssl_encrypt($data, self::CIPHER_ALGORITHM, $key, OPENSSL_RAW_DATA);
        
        if ($encrypted === false) {
            throw new \Exception('AES加密失败');
        }
        
        return base64_encode($encrypted);
    }
    
    /**
     * AES解密
     *
     * @param string $data 待解密的Base64字符串
     * @param string $password 密钥（16位）
     * @return string 解密后的原始数据
     * @throws \Exception
     */
    public static function decrypt(string $data, string $password): string
    {
        if (strlen($password) !== 16) {
            throw new \Exception('密钥长度必须为16位');
        }
        
        $key = self::generateKey($password);
        $encryptedData = base64_decode($data);
        
        if ($encryptedData === false) {
            throw new \Exception('Base64解码失败');
        }
        
        $decrypted = openssl_decrypt($encryptedData, self::CIPHER_ALGORITHM, $key, OPENSSL_RAW_DATA);
        
        if ($decrypted === false) {
            throw new \Exception('AES解密失败');
        }
        
        return $decrypted;
    }
    
    /**
     * 生成密钥
     * 模拟Java中的SecureRandom和KeyGenerator行为
     *
     * @param string $password 原始密码
     * @return string 生成的密钥
     */
    private static function generateKey(string $password): string
    {
        // 使用SHA1算法对密码进行哈希，然后截取前16字节作为密钥
        $hash = sha1($password, true);
        return substr($hash, 0, 16);
    }
    
    /**
     * MD5加密（用于某些场景下的数据验证）
     *
     * @param string $data 待加密数据
     * @return string MD5哈希值
     */
    public static function md5Encrypt(string $data): string
    {
        return md5($data);
    }
}
