<?php

namespace App\Utils\SmartCall;

/**
 * 智能外呼系统认证工具类
 */
class SmartCallAuth
{
    /**
     * 获取请求查询字符串
     * 对 Query 参数按照字典对 Key 进行排序后,按照value1+value2方法拼接
     *
     * @param array $params 请求参数
     * @return string
     */
    public static function getRequestQueryStr(array $params): string
    {
        if (empty($params)) {
            return '';
        }
        
        // 对参数按照键名排序
        ksort($params);
        
        // 拼接参数值
        $requestQuery = '';
        foreach ($params as $value) {
            $requestQuery .= $value === null ? '' : $value;
        }
        
        return $requestQuery;
    }
    
    /**
     * 计算请求签名值
     *
     * @param string $message 待签名字符串
     * @param string $secret 密钥APP SECRET
     * @return string HmacSHA256计算后摘要值的16进制字符串
     */
    public static function getSignature(string $message, string $secret): string
    {
        $algorithm = 'sha256';
        $hash = hash_hmac($algorithm, $message, $secret, true);
        return bin2hex($hash);
    }
    
    /**
     * 生成请求头
     *
     * @param string $requestQuery 请求查询字符串
     * @param string $requestBody 请求体内容
     * @param string $appKey 应用Key
     * @param string $appSecret 应用Secret
     * @param string $appType 应用类型（外部短信渠道标签，可选）
     * @return array 请求头数组
     */
    public static function generateHeaders(string $requestQuery, string $requestBody, string $appKey, string $appSecret, string $appType = ''): array
    {
        // 获取当前时间戳（毫秒）
        $timestamp = round(microtime(true) * 1000);
        
        // 构建待签名字符串：时间+请求串+请求体
        $message = $timestamp . $requestQuery . $requestBody;
        
        // 计算签名
        $signature = self::getSignature($message, $appSecret);
        
        // 构建请求头
        $headers = [
            'X-YS-APIKEY' => $appKey,
            'X-YS-TIME' => (string)$timestamp,
            'X-YS-SIGNATURE' => $signature,
        ];
        
        // 如果有应用类型，添加到请求头
        if (!empty($appType)) {
            $headers['X-YS-APPTYPE'] = $appType;
        }
        
        return $headers;
    }
}
