<?php

/*
 * 统一的路由前缀  mns-callback
 */

Route::post("/demo", function () {
    $postJson = file_get_contents("php://input");
    file_put_contents("/tmp/openapi-mns-callback.log", $postJson . "\n", FILE_APPEND);
    echo "ok";
});

Route::namespace('AfterSale')->group(function () {
    Route::post("/ticket-pm-delay", 'TicketPmController@delayCheck');
});

Route::namespace('Todo')->group(function () {
    Route::post("/create-todo-by-dts-message", 'TodoController@createTodoByDtsEvent');
    Route::post("/deal-todo-by-dts-message", 'TodoController@dealTodoByDtsEvent');
});

Route::prefix('order')
    ->namespace('Order')
    ->group(function () {
        // 设置确定性交付订单出结果时间
        Route::post('/set-assured-fulfill-result-time', 'OrderAssuredFulfillController@setResultTime');
        // 订单分派回调处理
        Route::post('/allot-callback', 'AllotController@orderAllotCallback');
        // 报告上传回调处理
        Route::post('/report-uploaded-callback', 'OrderReportController@reportUploadedCallback');
        // 订单超期-预计出结果时间监听
        Route::post('/order-overdue-callback', 'OrderOverdueController@orderOverdueCallback');
        // 订单超期-延迟队列回调处理
        Route::post('/order-overdue-delay', 'OrderOverdueController@handleOrderOverdue');
    });

Route::prefix('order-sampling')
    ->namespace('Order')
    ->group(function () {
        // 更新取样时间
        Route::post('/set-time-remind', 'OrderSamplingController@setSamplingTimeRemind');
    });

Route::prefix('infra')
    ->namespace('Infra')
    ->group(function () {
        // oss回调
        Route::post('/oss/notify-html-upload', 'OSSController@notifyHtmlUpload');
    });

Route::prefix('supplier')
    ->namespace('Supplier')
    ->group(function () {
        // 更新取样时间
        Route::post('/event-from-dts', 'EventNotificationReceiveController@eventFromDts');
    });


Route::namespace('Invoice')->group(function () {
    //生成发票相关文件
    Route::post('/invoice_related_file_generate_callback', 'InvoiceRelatedFileController@invoiceRelatedFileGenerateCallback');
});
Route::prefix('provider-merchant-payment')
    ->namespace('ProviderMerchantPayment')
    ->group(function () {
        //创建易快报单据
        Route::post('/create-ekuaibao-flow', 'EkuaibaoController@createEkuaibaoFlowByMnsCallback');
    });