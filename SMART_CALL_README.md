# 智能外呼系统集成

## 概述

本项目已成功集成智能外呼系统功能，提供完整的API接口和工具类，支持外呼任务管理、客户导入、结果查询等核心功能。

## 新增文件列表

### 1. 配置文件
- `config/constants.php` - 添加智能外呼系统配置项
- `smart_call_env_example.txt` - 环境配置示例

### 2. 工具类
- `app/Utils/SmartCall/SmartCallAuth.php` - 认证和签名工具类
- `app/Utils/SmartCall/SmartCallAESUtil.php` - AES加密工具类

### 3. 服务类
- `app/Services/SmartCall/SmartCallClient.php` - 智能外呼API客户端
- `app/Services/SmartCall/SmartCallService.php` - 智能外呼业务服务类

### 4. 控制器
- `app/Http/Controllers/SmartCallController.php` - 智能外呼控制器

### 5. 路由
- `routes/api.php` - 添加智能外呼相关路由

### 6. 文档和测试
- `SMART_CALL_USAGE.md` - 详细使用说明
- `tests/SmartCallTest.php` - 功能测试脚本
- `SMART_CALL_README.md` - 本文件

## 核心功能

### 1. 权限校验
- 自动生成HMAC-SHA256签名
- 处理请求头认证信息
- 支持外部短信渠道标签

### 2. 敏感字段加密
- AES-128-ECB加密模式
- 16位密钥支持
- Base64编码输出
- MD5哈希功能

### 3. API接口
- 客户信息导入（支持批量）
- 外呼任务管理（创建、暂停、恢复、停止）
- 任务状态查询
- 外呼结果查询
- 通话录音获取

### 4. 频率限制处理
- 自动分批处理大量数据
- 智能延迟避免频率限制
- 错误重试机制

## 技术特性

### 1. 安全性
- 敏感信息加密传输
- 签名验证防篡改
- 密钥安全管理

### 2. 可靠性
- 完整的错误处理
- 详细的日志记录
- 异常情况监控

### 3. 易用性
- 简洁的API接口
- 详细的使用文档
- 完整的代码示例

### 4. 扩展性
- 模块化设计
- 易于添加新功能
- 支持自定义配置

## 快速开始

### 1. 配置环境
```env
SMART_CALL_URL=https://your-api-url.com
SMART_CALL_APP_KEY=your_app_key
SMART_CALL_APP_SECRET=your_app_secret
SMART_CALL_APP_TYPE=your_channel_tag
```

### 2. 导入客户
```php
use App\Services\SmartCall\SmartCallService;

$service = new SmartCallService();
$customers = [
    ['name' => '张三', 'phone' => '13800138000', 'task_id' => 'task_123']
];
$result = $service->importCustomers($customers);
```

### 3. 创建任务
```php
$taskData = [
    'task_name' => '测试任务',
    'template_id' => 'template_123'
];
$task = $service->createCallTask($taskData);
```

### 4. 查询结果
```php
$results = $service->getCallResults($task['task_id']);
```

## API路由

| 方法 | 路径 | 功能 |
|------|------|------|
| POST | `/api/smart-call/customers/import` | 导入客户信息 |
| POST | `/api/smart-call/task/create` | 创建外呼任务 |
| GET | `/api/smart-call/task/status` | 查询任务状态 |
| POST | `/api/smart-call/task/pause` | 暂停任务 |
| POST | `/api/smart-call/task/resume` | 恢复任务 |
| POST | `/api/smart-call/task/stop` | 停止任务 |
| GET | `/api/smart-call/call/results` | 查询外呼结果 |
| GET | `/api/smart-call/call/recording` | 获取通话录音 |
| POST | `/api/smart-call/test/encryption` | 测试加密功能 |

## 注意事项

### 1. 频率限制
- 每秒不超过100次调用
- 每十分钟不超过30000次调用
- 系统自动处理分批和延迟

### 2. 数据安全
- 电话号码等敏感信息建议加密
- 妥善保管AppKey和AppSecret
- 不要在前端代码中暴露密钥

### 3. 错误处理
- 监控错误日志
- 实现重试机制
- 处理网络异常

## 测试验证

运行测试脚本验证功能：
```bash
php tests/SmartCallTest.php
```

## 技术支持

如有问题，请：
1. 查看详细使用文档 `SMART_CALL_USAGE.md`
2. 检查系统日志
3. 运行测试脚本验证
4. 联系技术支持团队

## 版本信息

- 版本：v1.0.0
- 创建日期：2025-07-10
- 兼容性：Laravel 5.x+
- PHP版本：7.4+

## 更新日志

### v1.0.0 (2025-07-10)
- 初始版本发布
- 完整的智能外呼系统集成
- 支持所有核心功能
- 完善的文档和测试
