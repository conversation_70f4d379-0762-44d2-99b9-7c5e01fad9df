APP_NAME=openapi
APP_ENV=test
APP_KEY=base64:9VNtVzJXlaSA3qlbXj7zh0o32pf6GHgRkGOVK4zFwdg=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=rm-bp15dwo7zntui6qda.mysql.rds.aliyuncs.com
DB_PORT=3306
DB_DATABASE=scinew
DB_USERNAME=shiyanjia
DB_PASSWORD="Yanqu10yan+"

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=sciclubs
REDIS_PORT=6379
REDIS_CLIENT=phpredis

MAIL_DRIVER=smtp
MAIL_HOST=smtp.163.com
MAIL_PORT=465
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD="TAeNhFQ4xjp82tLV"
MAIL_ENCRYPTION=ssl

IP_LIMIT=127.0.0.1,*************,**************,***************,***************,************

LOG_DIR=/data/logs/

CONSTANTS_SHIYANJIA_URL="http://www.sciclubs.net"
CONSTANTS_NOTIFY_URL="http://notify.sciclubs.net"
CONSTANTS_CRM_URL="http://crm.sciclubs.net"
CONSTANTS_CRMAPI_URL="http://crmapi.sciclubs.net"
CONSTANTS_ACTIVITY_URL="http://activity.sciclubs.net"
CONSTANTS_REPORT_SERVICE_URL="http://standard-report.sciclubs.net/standard-report"
CONSTANTS_CRM_V2_URL="http://crmv2.sciclubs.net"
CONSTANTS_NEXT_CRM_URL="http://next-crm.sciclubs.net"
CONSTANTS_SUPPLIER_URL="http://s.sciclubs.net"
CONSTANTS_ADMIN_URL="http://admin.sciclubs.net"
#小程序的环境参数 正式版为 release，体验版为 trial，开发版为 develop
CONSTANTS_ENV_VERSION="trial"
#团体券包id
CONSTANTS_GROUP_COUPONID=792
CONSTANTS_AI_SERVICE_URL="http://**************:12888"
#阿里云市场调用参数
CONSTANTS_APPCODE=4aa9c801cf744c4d8f95b6b8974ef9de
CONSTANTS_APPSECRET=4aa9c801cf744c4d8f95b6b8974ef9de
CONSTANTS_APPKEY=23605472
CONSTANTS_DATA_SERVICE_API=http://127.0.0.1:12306

SMS_API="http://127.0.0.1:82/sendSms/index.php?op=sendSms&cip=%s&content=%s&appid=1&mobile=%s"
SMS_SEND="http://127.0.0.1:8002/send_sms"


INVOICE_NNFP_APP_KEY="SD63236305"
INVOICE_NNFP_APP_SECRET="SDDED2523BED4643"
INVOICE_NNFP_URL="https://sandbox.nuonuocs.cn/open/v1/services"
DIGITAL_INVOICE_NNFP_APP_KEY="SD56531018"
DIGITAL_INVOICE_NNFP_APP_SECRET="SD3C429B318C485E"
FAST_INVOICE_APP_KEY="92650482"
FAST_INVOICE_APP_SECRET="D5E0211357A84662"
NEXT_SHIYANJIA_BASE_URL="http://next.sciclubs.net"
K8S_INGRESS_IP=**********
CRM_SERVICE_HOST=crm.sciclubs.net

#探索型订单buffetid
EXPLORATORY_BUFFET_ID_LIST = 768,1233
EXPLORATORY_EXPERIMENTAL_UNIT_BUFFET_ID= 32,40


OSS_ACCESSKEY_ID=LTAI5tD969dazEmAJHVeQmeL
OSS_ACCESSKEY_SECRET=******************************
OSS_END_POINT=oss-cn-hangzhou.aliyuncs.com
OSS_INTRANET_END_POINT=oss-cn-hangzhou-internal.aliyuncs.com
OSS_REPORT_BUCKET=shiyanjia-reports-dev

OSS_PAD_ACCESSKEY_ID=LTAI5tD969dazEmAJHVeQmeL
OSS_PAD_ACCESSKEY_SECRET=******************************

ORDER_SUPPLEMENTARY_AUTH_GIVE_COUPONID=1882

AUTH_EXAMINE_NOTICE_ROLE_ID=518
AUTH_EXAMINE_NOTICE_LEGAL_ROLE_ID=531

NUONUO_INVOICE_WECOM_ROBOT_WEBHOOK_URL='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3c83caf6-ad34-4c43-936f-b7e38a3ff20e'
#顺丰开放平台配置
SF_ACCESS_TOKEN_API=https://sfapi.sf-express.com/oauth2/accessToken
SF_PARTNER_ID=YQXXJX1LC0FQ
SF_SECRET=lXn4UphbfTux64f5jJz5QK77W1BhuIyv

TIAN_YAN_CHA_SEARCH_TOKEN='1fdcea86-4868-4543-ba80-8674ca42ba4a'
TIAN_YAN_CHA_SEARCH_CORPORATION_TOKEN='9f9e6766-9950-4572-8b80-7e8b06181895'

LE_CHENG_APPID="lc3f60cc6926a24447"
LE_CHENG_APP_SECRET="******************************"
LE_CHENG_URL="https://openapi.lechange.cn:443/openapi"
LE_HLS_KEYWORD = "http://"

SEND_WECHAT_MESSAGE_IP="**************"

CRM_ACCOUNT_ID_SYSTEM=1142
ADMIN_ACCOUNT_ID_SYSTEM=398

#纳米商城默认不可用优惠券id
NANO_UN_AVAILABLE_COUPON_IDS=2477,1993,2352,2353,841,11,1234

CREATE_NOT_AUTH_GROUP_PERMISSION=564

#百度接口配置
BAIDU_INVOICE_API_KEY=XGhcNEU2hrugPXddKMcuO2Pd
BAIDU_INVOICE_SECRET_KEY=7GjHbo2qn5Squxz8PYYQpWP6uNFQZ5Gh

#易快报接口配置
EKUAIBAO_URL_PREFIX=https://app.ekuaibao.com
EKUAIBAO_APP_KEY=a50210c3-d964-457b-8c11-83ceb14ba1d7
EKUAIBAO_APP_SECURITY=25ab1218-284e-4e09-a07f-9af11c2899ae

#业务相关
REPORT_UPLOADED_NOTIFY_BUFFET_IDS=228

#Yanqulib
YANQU_LIB_PATH=/home/<USER>/yanqu-phplib/

##mns主题名称
MNS_COMMON_EVENT_TOPIC_NAME=test-common-event