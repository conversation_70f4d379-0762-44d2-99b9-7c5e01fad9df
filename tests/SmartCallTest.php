<?php

/**
 * 智能外呼系统功能测试脚本
 * 
 * 使用方法：
 * 1. 配置好 .env 文件中的智能外呼系统参数
 * 2. 运行此脚本进行功能测试
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Utils\SmartCall\SmartCallAuth;
use App\Utils\SmartCall\SmartCallAESUtil;

class SmartCallTest
{
    public function testAuth()
    {
        echo "=== 测试认证功能 ===\n";
        
        $appKey = 'test_app_key';
        $appSecret = 'test_app_secret';
        $requestQuery = 'value1value2value3';
        $requestBody = '{"test": "data"}';
        
        $headers = SmartCallAuth::generateHeaders($requestQuery, $requestBody, $appKey, $appSecret);
        
        echo "生成的请求头:\n";
        foreach ($headers as $key => $value) {
            if ($key === 'X-YS-SIGNATURE') {
                echo "{$key}: ***\n";
            } else {
                echo "{$key}: {$value}\n";
            }
        }
        
        // 验证签名
        $timestamp = $headers['X-YS-TIME'];
        $message = $timestamp . $requestQuery . $requestBody;
        $expectedSignature = SmartCallAuth::getSignature($message, $appSecret);
        
        $isValid = $headers['X-YS-SIGNATURE'] === $expectedSignature;
        echo "签名验证: " . ($isValid ? "通过" : "失败") . "\n\n";
        
        return $isValid;
    }
    
    public function testEncryption()
    {
        echo "=== 测试加密功能 ===\n";
        
        $originalData = '13800138000';
        $encryptKey = '1234567890123456';
        
        try {
            // 测试加密
            $encrypted = SmartCallAESUtil::encrypt($originalData, $encryptKey);
            echo "原始数据: {$originalData}\n";
            echo "加密结果: {$encrypted}\n";
            
            // 测试解密
            $decrypted = SmartCallAESUtil::decrypt($encrypted, $encryptKey);
            echo "解密结果: {$decrypted}\n";
            
            // 验证
            $isValid = $originalData === $decrypted;
            echo "加密解密验证: " . ($isValid ? "通过" : "失败") . "\n";
            
            // 测试MD5
            $md5Hash = SmartCallAESUtil::md5Encrypt($originalData);
            echo "MD5哈希: {$md5Hash}\n\n";
            
            return $isValid;
            
        } catch (Exception $e) {
            echo "加密测试失败: " . $e->getMessage() . "\n\n";
            return false;
        }
    }
    
    public function testRequestQueryStr()
    {
        echo "=== 测试请求查询字符串生成 ===\n";
        
        $params = [
            'c' => 'value3',
            'a' => 'value1', 
            'b' => 'value2'
        ];
        
        $queryStr = SmartCallAuth::getRequestQueryStr($params);
        $expected = 'value1value2value3'; // 按字典序排序后拼接
        
        echo "输入参数: " . json_encode($params) . "\n";
        echo "生成结果: {$queryStr}\n";
        echo "期望结果: {$expected}\n";
        
        $isValid = $queryStr === $expected;
        echo "验证结果: " . ($isValid ? "通过" : "失败") . "\n\n";
        
        return $isValid;
    }
    
    public function runAllTests()
    {
        echo "开始智能外呼系统功能测试...\n\n";
        
        $results = [
            'auth' => $this->testAuth(),
            'encryption' => $this->testEncryption(),
            'query_string' => $this->testRequestQueryStr()
        ];
        
        echo "=== 测试结果汇总 ===\n";
        $allPassed = true;
        foreach ($results as $test => $result) {
            echo "{$test}: " . ($result ? "通过" : "失败") . "\n";
            if (!$result) {
                $allPassed = false;
            }
        }
        
        echo "\n总体结果: " . ($allPassed ? "所有测试通过" : "存在测试失败") . "\n";
        
        return $allPassed;
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new SmartCallTest();
    $test->runAllTests();
}
