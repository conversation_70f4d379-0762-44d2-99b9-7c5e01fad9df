# 智能外呼系统使用说明

## 概述

本项目已集成智能外呼系统功能，提供完整的外呼任务管理、客户导入、结果查询等功能。

## 配置

### 1. 环境配置

在 `.env` 文件中添加以下配置：

```env
# 智能外呼系统基础URL
SMART_CALL_URL=https://your-smart-call-api.com

# 智能外呼系统应用密钥
SMART_CALL_APP_KEY=your_app_key_here
SMART_CALL_APP_SECRET=your_app_secret_here

# 外部短信渠道标签（可选）
SMART_CALL_APP_TYPE=your_channel_tag
```

### 2. 权限校验

系统自动处理以下HTTP Header：
- `X-YS-APIKEY`: 应用Key
- `X-YS-TIME`: 当前时间戳（毫秒）
- `X-YS-SIGNATURE`: HMAC-SHA256签名
- `X-YS-APPTYPE`: 应用类型（可选）

### 3. 签名算法

签名字符串 = 时间戳 + 请求查询字符串 + 请求体
使用HMAC-SHA256算法计算签名。

## API接口

### 客户管理

#### 导入客户信息
```http
POST /api/smart-call/customers/import
Content-Type: application/json

{
    "customers": [
        {
            "name": "张三",
            "phone": "13800138000",
            "task_id": "task_123"
        }
    ],
    "encrypt_key": "1234567890123456"  // 可选，16位加密密钥
}
```

**注意事项：**
- 每秒钟不能调用超过100次
- 每十分钟的调用总次数不能超过30000次
- 支持批量导入，超过100条记录时自动分批处理

### 任务管理

#### 创建外呼任务
```http
POST /api/smart-call/task/create
Content-Type: application/json

{
    "task_name": "测试外呼任务",
    "template_id": "template_123",
    "call_time_start": "09:00",
    "call_time_end": "18:00",
    "max_call_count": 3,
    "call_interval": 60
}
```

#### 查询任务状态
```http
GET /api/smart-call/task/status?task_id=task_123
```

#### 暂停任务
```http
POST /api/smart-call/task/pause
Content-Type: application/json

{
    "task_id": "task_123"
}
```

#### 恢复任务
```http
POST /api/smart-call/task/resume
Content-Type: application/json

{
    "task_id": "task_123"
}
```

#### 停止任务
```http
POST /api/smart-call/task/stop
Content-Type: application/json

{
    "task_id": "task_123"
}
```

### 结果查询

#### 查询外呼结果
```http
GET /api/smart-call/call/results?task_id=task_123&page=1&page_size=50
```

#### 获取通话录音
```http
GET /api/smart-call/call/recording?call_id=call_123
```

### 工具接口

#### 测试加密功能
```http
POST /api/smart-call/test/encryption
Content-Type: application/json

{
    "data": "13800138000",
    "encrypt_key": "1234567890123456"
}
```

## 敏感字段加密

### AES加密

系统支持对敏感字段（如电话号码）进行AES加密：

```php
use App\Services\SmartCall\SmartCallClient;

$client = new SmartCallClient();

// 加密
$encrypted = $client->encryptSensitiveField('13800138000', '1234567890123456');

// 解密
$decrypted = $client->decryptSensitiveField($encrypted, '1234567890123456');

// MD5哈希
$hash = $client->getMd5Hash('13800138000');
```

### 加密要求

- 密钥长度必须为16位
- 使用AES-128-ECB模式
- 输出Base64编码

## 代码示例

### 基础使用

```php
use App\Services\SmartCall\SmartCallService;

$service = new SmartCallService();

// 导入客户
$customers = [
    [
        'name' => '张三',
        'phone' => '13800138000',
        'task_id' => 'task_123'
    ]
];

$result = $service->importCustomers($customers);

// 创建任务
$taskData = [
    'task_name' => '测试任务',
    'template_id' => 'template_123'
];

$task = $service->createCallTask($taskData);

// 查询结果
$results = $service->getCallResults($task['task_id']);
```

### 批量导入

```php
// 大批量客户数据
$customers = []; // 假设有1000条客户数据

// 自动分批处理，避免频率限制
$result = $service->batchImportCustomers($customers, '1234567890123456', 100);

echo "总数: {$result['total_count']}\n";
echo "成功: {$result['success_count']}\n";
echo "失败: {$result['failed_count']}\n";
```

## 错误处理

### 常见错误码

- `1037`: 每秒调用频率超限
- `1038`: 十分钟调用总次数超限
- `200`: 成功

### 异常处理

```php
try {
    $result = $service->importCustomers($customers);
} catch (\Exception $e) {
    // 记录错误日志
    \Yanqu\YanquPhplib\YqLog\YqLog::logger('smart_call:error')
        ->error('操作失败', ['error' => $e->getMessage()]);
    
    // 处理错误
    return response()->json(['error' => $e->getMessage()], 500);
}
```

## 日志记录

系统自动记录以下日志：

- `smart_call:request` - 请求日志
- `smart_call:response` - 响应日志
- `smart_call:error` - 错误日志
- `smart_call:import_customers` - 客户导入日志
- `smart_call:create_task` - 任务创建日志

## 注意事项

1. **频率限制**: 严格遵守API调用频率限制
2. **敏感数据**: 电话号码等敏感信息建议加密传输
3. **错误重试**: 遇到频率限制时应实现指数退避重试
4. **日志监控**: 关注错误日志，及时处理异常情况
5. **配置安全**: 妥善保管AppKey和AppSecret，不要在前端代码中暴露

## 扩展功能

如需添加新的API接口，请：

1. 在 `SmartCallService` 中添加对应方法
2. 在 `SmartCallController` 中添加控制器方法
3. 在 `routes/api.php` 中添加路由
4. 更新本文档

## 技术支持

如有问题，请查看：
1. 系统日志文件
2. 智能外呼系统官方文档
3. 联系技术支持团队
